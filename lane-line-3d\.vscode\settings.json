{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*", "lint-staged*": ".editorconfig, .eslint*, eslint*, .prettier*, prettier*, stylelint*, commitlint*", "postcss.config.*": "tailwind*"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules\\typescript\\lib"}