{"name": "editor", "version": "0.0.0", "private": true, "type": "module", "scripts": {"prepare": "husky", "dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "run-p --continue-on-error lint:*", "lint:script": "eslint . --fix", "lint:style": "stylelint \"**/*.{vue,scss,css}\" --fix", "lint:others": "prettier --write \"**/*.{json,md}\""}, "engines": {"node": ">=18.0.0", "pnpm": ">=9.0.0"}, "packageManager": "pnpm@9.15.9", "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@floating-ui/vue": "^1.1.6", "@tweenjs/tween.js": "^25.0.0", "@vueuse/core": "^13.1.0", "ant-design-vue": "^3.2.20", "axios": "^1.8.4", "color-hash": "^2.0.2", "colord": "^2.9.3", "dayjs": "^1.11.13", "hotkeys-js": "^3.13.9", "interactjs": "^1.10.27", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "mustache": "^4.2.0", "mutative": "^1.1.0", "nanoid": "^5.1.5", "query-string": "^7.1.3", "three": "^0.136.0", "ua-parser-js": "^1.0.40", "vue": "^3.5.13", "vue-clipboard2": "^0.3.3", "vue-router": "^4.5.1", "vue3-colorpicker": "^2.3.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@tsconfig/node20": "^20.1.5", "@types/color-hash": "^2.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/mustache": "^4.2.5", "@types/node": "^20.17.30", "@types/three": "^0.136.1", "@types/ua-parser-js": "^0.7.39", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^9.33.0", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^15.5.1", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.87.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-prettier": "^5.0.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.3", "vue-tsc": "^2.2.10"}}