<script setup lang="ts">
import { ConfigProvider } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'

import Editor from '@/editors/LaneLine3D/LaneLine3D.vue'
import useToken from '@/hooks/useToken'

const token = useToken()
if (!token) {
  message.error('请先登录')
}
</script>

<template>
  <ConfigProvider :locale="zhCN">
    <Editor v-if="token" />
    <template v-else>
      <div class="flex h-screen items-center justify-center text-white">请先登录</div>
    </template>
  </ConfigProvider>
</template>

<style scoped lang="scss"></style>
