# API 模块

## 定义接口

- **务必为接口声明入参类型和返回类型**
  - 应该在 `@/api/typings` 中创建同名 `d.ts` 文件来定义类型（后端提供了字段注释也要加上）
  - 公共类型放在 `@/api/typings/index.d.ts` 上
- 定义接口时，参数一般不应超过 2 个，如需传入 `AxiosRequestConfig` 应始终将其作为最后一个参数

  ```ts
  /**
   * @param idOrParams 数据型参数，可能是实体的索引或一个普通对象。
   * 只有接口仅需 id 或 ids 时，才使用 `string | number | string[] | number[]` 类型。
   * 不要在这里传 `{ url, params, data, header }` 的格式，而是使用该参数来构造。
   * 非普通对象如 FormData 应该在接口内生成。
   * @param {object} options 配置型参数。通常是 AxiosRequestConfig，也有可能包含一些自定义字段
   */
  type RequestExample = (
    idOrParams?: string | number | string[] | number[] | Record<string, any>,
    options?: AxiosRequestConfig & Record<string, any>,
  ) => any

  function updateUser(user: User, options?: AxiosRequestConfig) {
    const { uid, ...body } = params
    const data = new FormData()
    for (const [key, value] of Object.entries(body)) {
      data.append(key, value)
    }
    return axios.put<{}, User>(`/users/${uid}`, { ...options, data })
  }
  ```

### 常用接口命名前缀

- `get` 查询单个对象（也可以返回列表，如 getMenu、getOptions，取决于是否为整体）
- `list` 查询分页列表
- `create` 创建对象
- `update` 更新对象（传完整的新对象）
- `patch` 更新对象（传新对象已改变的值）
- `modify` 定向修改对象的某些属性
- `delete` 删除对象
- `batchDelete` 批量删除对象
- `upload` 上传（上传一般只会将文件放在服务器，生成一个链接）
- `import` 导入（导入主要是提取文件的数据，文件本身不会被用户访问）
- `export`
- `is/has/should`

## 使用接口

```ts
/**
 * 一般无返回值使用 load 而不是 get
 */
const loadData = async () => {
  try {
    this.loading = true
    /**
     * 返回值为 { code, message, data }
     */
    const { data } = await getProductDetail({
      id: this.id,
    })
    this.data = data
  } catch (e) {
    /**
     * code 不为 200 时会抛出异常
     * e.code 网络异常时为 -1，否则为返回的 code
     * e.message 返回的 message，网络异常时有相应的提示语
     */
    this.$message.error(e.message)
  } finally {
    this.loading = false
  }
}
```
