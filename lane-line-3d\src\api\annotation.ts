import { attrsToValues } from '@/utils/attrs'

import axios from './axios'
import type { AnnotationData, SaveData, SaveResponseItem } from './typings/annotation'

export async function getAnnotationData(dataIds: string[] | string) {
  if (!Array.isArray(dataIds)) dataIds = [dataIds]

  let data = await axios.get<any, AnnotationData[]>('/loadAnnotationByIds', {
    params: {
      dataIds,
    },
  })
  data = data || []
  const items = data.map((e) => {
    const { dataId, objects, classificationValues } = e
    return {
      dataId,
      objects,
      classificationData: classificationValues.reduce(
        (map, c) => {
          return Object.assign(map, attrsToValues(c.classificationAttributes.values))
        },
        {} as Record<string, any>,
      ),
    }
  })
  return items
}

export async function saveAnnotationData(frameDataList: SaveData) {
  return await axios.post<any, SaveResponseItem[]>('/saveAnnotate', frameDataList)
}
