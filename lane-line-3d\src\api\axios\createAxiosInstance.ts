import axios from 'axios'
import { AxiosError, type AxiosRequestConfig } from 'axios'
import { defaultsDeep } from 'lodash-es'

import createCustomAxiosError from './createCustomAxiosError'

declare module 'axios' {
  interface AxiosRequestConfig {
    injectCommonPayload?: () => Pick<AxiosRequestConfig, 'headers' | 'params' | 'data'>
    /**
     * 许多后端规范会要求接口无论成功失败 http status 都始终为 200，
     * 并返回诸如 { data, code, message, success } 的数据结构，
     * 以 success（没有则以 code）作为是否成功的判断标识，
     * 此时需要根据项目实际定义的数据格式规则来决定如何处理响应结果，
     * 如果为错误响应则使用返回的错误码和错误信息生成异常并抛出，
     * 如果为正常响应则解包并返回实际的数据。
     */
    resolveResponse?:
      | ((response: AxiosResponse, createCustomAxiosError1: typeof createCustomAxiosError) => any)
      | null
      | false
    /**
     * 对于标准 restful 接口，业务错误时 http status 为 4xx/5xx，
     * 并返回诸如 { code, message } 的数据结构，
     * 此时使用数据体内的错误码和错误信息生成异常并抛出。
     */
    resolveHttpError?:
      | ((error: AxiosError, createCustomAxiosError1: typeof createCustomAxiosError) => any)
      | null
      | false
    /**
     * 请求有异常会调用所有拦截器并作为返回结果。
     * 注意：如果不将异常重新抛出，命中该错误码的请求不会进入 catch！
     */
    errorInterceptors?: Array<{
      test: (error: AxiosError) => boolean
      func: (error: AxiosError, axiosInstance: Axios) => any
    }>
    /**
     * 异常请求没有命中任何拦截器时默认会把错误信息进行提示。
     */
    displayError?: ((error: AxiosError) => void) | null | false

    httpStatusToMessage?: (status: number) => string

    internalErrorCodeToMessage?: (code: string) => string
  }

  interface AxiosError {
    /**
     * AxiosError 已使用了 code，故使用 errorCode
     * 1. 通常是来自后端定义的错误码
     * 2. http 状态码（4xx、5xx）
     * 3. xhr.onerror | xhr.onabort | xhr.ontimeout，统一按网络异常处理，错误码设为 -1
     */
    errorCode: number
  }
}

export default function createAxiosInstance(instanceConfig: AxiosRequestConfig) {
  const instance = axios.create(instanceConfig)

  instance.interceptors.request.use((request) => {
    if (request.injectCommonPayload) {
      defaultsDeep(request, request.injectCommonPayload())
    }
    return request
  })

  /**
   * 响应异常包括:
   * xhr.onerror - 断网或不允许的跨域。有 error.response，但实际是 request 对象，request.status 为 0
   * xhr.onabort - 调用了 xhr.abort。没有 error.response，使用 axios 一般不会出现
   * xhr.ontimeout - 请求超时。没有 error.response
   * cancel - 使用 axios 的 cancelToken 来取消请求。没有 error.response
   * http 状态码异常 - 可能是服务器未捕捉的异常、服务器主动返回的 http 错误、来自网关的错误。
   * http 通过响应结果返回的异常 - http 状态码正常，但数据格式符合服务器自定义错误格式。
   */
  instance.interceptors.response.use(
    function tryToResolveResponse(response) {
      const { config } = response

      if (config.resolveResponse) {
        return config.resolveResponse(response, createCustomAxiosError)
      }

      return response
    },
    function tryToResolveHttpError(error: AxiosError) {
      const { config } = error

      if (config?.resolveHttpError) {
        return config.resolveHttpError(error, createCustomAxiosError)
      }

      throw error
    },
  )

  instance.interceptors.response.use(
    undefined,
    // 需要加工的非预期异常：1. onerror、onabort、ontimeout；2. http 状态码异常（4xx、5xx）
    function normalizeErrorMessage(error: any) {
      if (axios.isCancel(error)) {
        // cancel 不做处理
        throw error
      }
      if (error instanceof AxiosError) {
        let standardizedMessage = ''
        if (error.errorCode) {
          // 接口返回的自定义错误信息为最高优先级
          standardizedMessage = error.message
        }
        if (!standardizedMessage && error.response?.status && error.config?.httpStatusToMessage) {
          standardizedMessage = error.config.httpStatusToMessage(error.response?.status)
        }
        if (!standardizedMessage && error.code && error.config?.internalErrorCodeToMessage) {
          standardizedMessage = error.config.internalErrorCodeToMessage(error.code)
        }
        if (standardizedMessage) {
          Object.assign(error, { message: standardizedMessage })
        }
      }
      throw error
    },
  )

  instance.interceptors.response.use(undefined, function commonErrorHandler(error: AxiosError) {
    const displayError = error.config?.displayError
    if (displayError) {
      displayError(error)
    }
    throw error
  })

  instance.interceptors.response.use(undefined, function commonErrorHandler(error: AxiosError) {
    const errorInterceptors = error.config?.errorInterceptors
    if (errorInterceptors) {
      const list = Object.values(errorInterceptors)
      for (const item of list) {
        let matched = false
        try {
          matched = item!.test(error)
        } catch {
          //
        }
        if (matched) {
          return item!.func(error, instance)
        }
      }
    }
    throw error
  })

  return instance
}
