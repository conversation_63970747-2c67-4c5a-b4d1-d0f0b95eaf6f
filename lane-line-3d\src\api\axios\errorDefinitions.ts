import { AxiosError, type AxiosRequestConfig } from 'axios'

export const codeTypes = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  GATEWAY_TIMEOUT: 504,
  REDIRECT: 3002,
}

/**
 * 这里只定义非预期异常的错误提示，因为后端返回自定义异常时会返回对应的错误提示
 */
export const codeMessages: Record<string, string> = {
  [AxiosError.ECONNABORTED]: '请检查网络！', // onabort
  [AxiosError.ERR_NETWORK]: '请检查网络！', // onerror
  [AxiosError.ETIMEDOUT]: '请检查网络！', // ontimeout
  [codeTypes.UNAUTHORIZED]: '认证失败，无法访问系统资源',
  [codeTypes.FORBIDDEN]: '当前操作没有权限',
  [codeTypes.NOT_FOUND]: '访问资源不存在',
  [codeTypes.GATEWAY_TIMEOUT]: '服务器网关超时！',
}

declare module 'axios' {
  interface AxiosRequestConfig {
    retryTimes?: number
  }
}

/**
 * 注意：如果不将异常重新抛出，命中该错误码的请求不会进入 catch！
 */
export const errorInterceptors: AxiosRequestConfig['errorInterceptors'] = []
