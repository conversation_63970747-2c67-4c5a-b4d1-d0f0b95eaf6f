import { message } from 'ant-design-vue'
import qs from 'query-string'

import createAxiosInstance from './createAxiosInstance'
import { codeMessages, codeTypes, errorInterceptors } from './errorDefinitions'

export { codeTypes }

/**
 * 1. 底层错误：code
 * 2. http错误：code + status
 * 3. 业务层错误：code + status + errorCode
 * 优先级：errorCode > status > code
 */
const instance = createAxiosInstance({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  withCredentials: false,
  timeout: 60000,
  headers: {
    // 'Content-Type': 'application/json',
  },
  paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'comma' }),
  async resolveResponse(response, createCustomAxiosError) {
    if (response.data instanceof Blob) {
      if (response.data.type === 'application/json') {
        response.data = JSON.parse(await response.data.text())
      } else {
        return response
      }
    }
    const KeyMap = {
      Code: 'code',
      Message: 'message',
    }
    const SUCCESS_CODE = 'OK'
    const { data } = response
    if (data && KeyMap.Code in data) {
      const statusCode = data[KeyMap.Code]
      if (statusCode !== SUCCESS_CODE) {
        // 响应的数据格式符合服务器自定义错误格式，取相应的自定义状态码和错误信息创建异常并抛出
        throw createCustomAxiosError(data[KeyMap.Message], statusCode, response)
      }
      if (data.rows) {
        return {
          rows: data.rows,
          total: data.total,
        }
      }
      return data.data
    }
    // 正常情况下不应该执行到这里
    return data
  },
  httpStatusToMessage(status: number) {
    return codeMessages[status] || '系统未知错误，请反馈给管理员'
  },
  internalErrorCodeToMessage(code: string) {
    return codeMessages[code]
  },
  displayError(error) {
    message.error({
      content: error.message,
    })
  },
  errorInterceptors,
})

export default instance
