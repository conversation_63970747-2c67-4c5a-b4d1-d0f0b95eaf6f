import type { IDataResource, IFileConfig } from '@/types'
import { convertClassificationDefinitions } from '@/utils/classification'
import { convertClassDefinitions } from '@/utils/classType'
import { createViewConfig } from '@/utils/common'

import axios from './axios'
import type {
  ClassificationDefinition,
  ClassTypeDefinition,
  FrameFileConfig,
  FrameStatusItem,
  LidarPoseData,
  SceneData,
} from './typings/common'

export async function getTaskInfo() {
  const data = await axios.get<any, SceneData>(`/findDataAnnotationRecord`)
  // no data
  if (!data || !data.datas || data.datas.length === 0) {
    return { dataInfos: [], isSeriesFrame: false }
  }

  const isSeriesFrame = ['FRAME_SERIES', 'SCENE'].includes(data.itemType)

  const dataInfos = (data.datas || []).map((config) => {
    return {
      id: config.dataId + '',
      name: '',
      datasetId: config.datasetId + '',
      pointsUrl: '',
      queryTime: '',
      loadState: '',
      needSave: false,
      classifications: [],
      dataStatus: 'VALID',
      annotationStatus: 'NOT_ANNOTATED',
      skipped: false,
    }
  })

  const ids = dataInfos.map((e) => e.id)
  const stateMap = await getFrameStatus(ids)
  dataInfos.forEach((info: any) => {
    const status = stateMap[info.id]
    if (!status) return
    info.name = status.name
    info.dataStatus = status.status || 'VALID'
    info.annotationStatus = status.annotationStatus || 'NOT_ANNOTATED'
  })

  return { dataInfos, isSeriesFrame }
}

async function getFrameStatus(dataIds: string[]) {
  const batchSize = 200
  const requests: Promise<FrameStatusItem[]>[] = []
  while (dataIds.length > 0) {
    const batchIds = dataIds.splice(0, batchSize)
    requests.push(
      axios.get<any, FrameStatusItem[]>('/getDataStatusByIds', {
        params: {
          dataIds: batchIds,
        },
      }),
    )
  }
  return Promise.all(requests).then((responses) => {
    const statusMap: Record<string, FrameStatusItem> = {}
    responses.forEach((res) => {
      res.forEach((item) => {
        statusMap[item.id] = item
      })
    })
    return statusMap
  })
}

export async function getFrameFileConfig(dataId: string) {
  const data = await axios.get<any, FrameFileConfig[]>(`/dataListByIds`, {
    params: {
      dataIds: dataId,
    },
  })

  const frame = data[0]

  const fileConfig: IFileConfig[] = frame.content.map((dir) => {
    const fileEntry = dir.files[0]
    let fileMeta = fileEntry.file
    if ('binary' in fileMeta) fileMeta = fileMeta.binary
    return {
      dirName: dir.name,
      name: fileEntry.name,
      url: fileMeta.url,
    }
  })

  const frameName = frame.name

  const regLidar = new RegExp(/point(_?)cloud/i)
  const regConfig = new RegExp(/camera(_?)config/i)
  const regPose = new RegExp(/pose(_?)info/i)
  if (fileConfig.filter((e: any) => regLidar.test(e.dirName)).length === 0) {
    throw new Error('no-point-data')
  }

  const cameraConfig = fileConfig.find((e) => regConfig.test(e.dirName)) as IFileConfig
  // no camera config
  let cameraInfo = []
  if (cameraConfig) {
    cameraInfo = await fetchResource(cameraConfig.url)
  }

  const poseInfo = fileConfig.find((e) => regPose.test(e.dirName)) as IFileConfig
  let poseData = undefined
  if (poseInfo) {
    poseData = await fetchResource(poseInfo.url)
  }

  const info = createViewConfig(fileConfig, cameraInfo)
  const config: IDataResource = {
    pointsUrl: info.pointsUrl,
    pointsData: {},
    pose: poseData,
    viewConfig: info.config,
    time: 0,
    name: frameName,
  }
  return config
}

export async function getFrameFileConfig2(dataId: string) {
  const data = await axios.get<any, FrameFileConfig[]>(`/dataListByIds`, {
    params: {
      dataIds: dataId,
    },
  })

  const frame = data[0]

  const dirConfig = frame.content.map((dir) => {
    return {
      dirName: dir.name,
      files: dir.files.map((fileEntry) => {
        let fileMeta = fileEntry.file
        if ('binary' in fileMeta) fileMeta = fileMeta.binary
        return {
          name: fileEntry.name,
          url: fileMeta.url,
        }
      }),
    }
  })

  const frameName = frame.name

  const regLidar = new RegExp(/point(_?)cloud/i)
  const regConfig = new RegExp(/camera(_?)config/i)
  const regPose = new RegExp(/pose(_?)info/i)
  const regHeightMap = new RegExp(/height(_?)map/i)
  const regImage = new RegExp(/image/i)

  const lidarDir = dirConfig.find((e) => regLidar.test(e.dirName))
  const pointCloudUrl = lidarDir?.files.find((e) => e.name.match(/.[pcd|bin]$/))?.url

  if (!pointCloudUrl) {
    throw new Error('no-point-data')
  }

  const cameraConfigDir = dirConfig.find((e) => regConfig.test(e.dirName))

  const poseInfoDir = dirConfig.find((e) => regPose.test(e.dirName))

  const cameraImageDirs = dirConfig
    .filter((e) => e.dirName.match(regImage))
    .sort((a, b) => {
      const aIndex = +(a.dirName.match(/[0-9]{1,5}$/) as any)[0]
      const bIndex = +(b.dirName.match(/[0-9]{1,5}$/) as any)[0]
      return aIndex - bIndex
    })

  const heightMapDir = dirConfig.find((e) => regHeightMap.test(e.dirName))
  let heightMapUrl = ''
  let heightMapMetaUrl = ''
  if (heightMapDir) {
    heightMapUrl = heightMapDir.files.find((e) => e.name.match(/.raw$/))?.url || ''
    heightMapMetaUrl = heightMapDir.files.find((e) => e.name.match(/.json$/))?.url || ''
  }

  return {
    frameName,
    time: 0,
    pointCloudUrl,
    frameResources: cameraConfigDir?.files.map((f, i) => {
      return {
        configUrl: f.url,
        poseUrl: poseInfoDir?.files[i].url,
        imageUrls: cameraImageDirs.map((e) => e.files[i].url),
      }
    }),
    heightMapUrl,
    heightMapMetaUrl,
  }
}

export async function getClassTypes() {
  const data = await axios.get<any, ClassTypeDefinition[]>(`/projectClassFindAll`)

  return convertClassDefinitions(data || [])
}

export async function getTaskFormAttrs() {
  const data = await axios.get<any, ClassificationDefinition[]>('/projectClassificationFindAll')

  return convertClassificationDefinitions(data || [])
}

export async function getTaskFormData() {
  return {}
}

export async function fetchResource(url: string) {
  return await axios.get<any, any>(url, { headers: { 'x-request-type': 'resource' } })
}

export async function getGlobalPoses() {
  const data = await axios.get<any, LidarPoseData[]>('/getglobalposes')
  return data.map((e) => e.content.lidar_pose.info)
}
