import axios from './axios'
import type { InferenceRequest, InferenceResponse, ModelInfo } from './typings/model'

export async function getModelList() {
  let data = await axios.get<any, ModelInfo[]>('/modelList')
  data = data || []

  return data
    .map((e) => {
      if (e.isInteractive) return
      const classes = (e.classes || []).map((e) => {
        return { label: e.name, value: e.code }
      })
      return {
        id: e.id + '',
        name: e.name,
        version: e.version,
        code: e.modelCode,
        classes,
        track: e.track,
      }
    })
    .filter((e) => !!e)
}

export async function startRunModel(config: InferenceRequest) {
  return await axios.post<any, string>('/modelAnnotate', config)
}

export async function getModelResult(dataIds: string[], taskId: string) {
  return await axios.get<any, InferenceResponse>('/modelAnnotationResult', {
    params: {
      serialNo: taskId,
      dataIds: dataIds,
    },
  })
}
