// 通用向量类型
export interface Vec3 {
  x: number
  y: number
  z: number
}

export interface Vec2 {
  x: number
  y: number
  z?: number
}

// 内部分类属性项
export interface ClassificationAttributeValue {
  id: string
  pid: string | null
  name: string
  alias: string
  type: string
  isLeaf: boolean
  value: string
}

// 每个 classification 对应的属性集合
export interface ClassificationAttributes {
  id: number
  values: ClassificationAttributeValue[]
}

// 数据级别分类值
export interface ClassificationValue {
  id: number
  datasetId: number
  taskId: number
  dataId: number
  classificationId: number
  classificationAttributes: ClassificationAttributes
}

// 轮廓信息
export interface Contour {
  viewIndex?: number
  pointN?: number
  points?: Vec3[] | Vec2[]
  center3D?: Vec3
  size3D?: Vec3
  rotation3D?: Vec3
}

// 类别属性值（如行人细分）
export interface ClassValue {
  id: string
  pid: string | null
  name: string
  alias: string
  type: string
  isLeaf: boolean
  value: string
}

// 元数据信息
export interface Meta {
  isProjection: boolean
  classType?: string
  color?: string
}

// 3D Box 的详细属性
export interface ClassAttributes {
  id: string
  type: string
  version: number
  trackId: string
  trackName: string
  frontId: string
  sourceId: number
  sourceType: string
  modelClass: string
  classId?: number | string
  className: string
  meta: Meta
  contour: Contour
  classValues: ClassValue[]
  createdAt: string
  createdBy: number
}

// 每个标注目标（如 3D box）
export interface AnnotatedObject {
  id: number
  datasetId: number
  taskId: number
  dataId: number
  classId?: number
  type: string
  camIndex: number
  classAttributes: ClassAttributes
  remark: string
  sourceType: string
  sourceId: number
  auditId: number
  auditStatus: string
}

// 顶层标注数据结构
export interface AnnotationData {
  dataId: number
  classificationValues: ClassificationValue[]
  objects: AnnotatedObject[]
  marks: any[] // 你目前没有 marks 结构，保持为 any[]
}

// 对象项
export interface ObjectItem {
  id?: number
  frontId: string
  classId?: number | string
  source: string
  sourceId: number
  sourceType: string
  classAttributes: ClassAttributes
  remark: string
}

// 数据信息
export interface DataInfo {
  dataId: string
  objects: ObjectItem[]
  dataAnnotations: DataAnnotation[]
}

// 数据标注
export interface DataAnnotation {
  classificationId: number
  classificationAttributes: ClassificationAttributes
}

// 根数据结构
export interface SaveData {
  datasetId: string
  dataInfos: DataInfo[]
}

export interface SaveResponseItem {
  dataId: number
  id: number
  frontId: string
}
