export interface FrameFileConfig {
  id: number
  datasetId: number
  name: string
  content: Directory[]
  type: string // e.g., "SCENE"
  status: string // e.g., "VALID"
  annotationStatus: string // e.g., "ANNOTATED"
}

export interface Directory {
  name: string
  type: 'directory'
  files: FileEntry[]
}

export interface FileEntry {
  name: string
  fileId: number
  type: 'file'
  file: FileMeta | { binary: FileMeta }
}

export interface FileMeta {
  id: number
  name: string
  originalName: string
  url: string
  path: string
  zipPath: string
  type: string // MIME type, e.g., "application/json"
  size: number
  buketName: string
}

export interface ClassificationDefinition {
  id: number
  datasetId: number
  name: string
  identification: string
  color: string
  type: string // e.g., "SCENE"
  attributes: Attribute[]
}

export interface Attribute {
  id: string
  name: string
  type: 'RADIO' | 'CHECKBOX' | 'TEXT' | string
  required: boolean
  options: AttributeOption[]
}

export interface AttributeOption {
  id: string
  name: string
  identification?: string // 可选字段
  default: boolean
  attributes: Attribute[] // 可支持嵌套属性
}

export interface ClassTypeDefinition extends ClassificationDefinition {
  '2d_attributes': Attribute[]
  '3d_attributes': Attribute[]
  toolTypeOptions: any
}

export interface FrameStatusItem {
  id: number
  datasetId: number
  name: string
  status: 'VALID' | 'INVALID' | string
  annotationStatus: 'ANNOTATED' | 'NOT_ANNOTATED' | string
}

export interface SceneData {
  datasetId: number
  itemType: 'SCENE' | 'FRAME_SERIES' | string
  datas: Array<{
    datasetId: number
    sceneId: number
    dataId: number
    modelId: number | null
    modelVersion: number | null
  }>
}

export interface LidarPoseData {
  name: string
  content: {
    lidar_pose: {
      info: {
        x: number
        y: number
        z: number
        w: number
        pose_x: number
        pose_y: number
        pose_z: number
      }
    }
    timestamp: number
  }
}
