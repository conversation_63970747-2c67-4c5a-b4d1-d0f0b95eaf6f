interface ClassItem {
  name: string
  code: string
}

export interface ModelInfo {
  id: number
  name: string
  description: string
  version: string
  datasetType: string
  track: boolean
  classes: ClassItem[]
  isInteractive?: boolean
  modelCode?: string
}

interface ResultFilterParam {
  minConfidence: number
  maxConfidence: number
  classes: string[]
}

export interface InferenceRequest {
  datasetId: string
  dataIds: number[]
  modelId: number
  modelVersion: string
  dataType: string
  resultFilterParam: ResultFilterParam
}

interface ResultFilterParam {
  classes: string[]
  minConfidence: number
  maxConfidence: number
}

interface ModelDataResult {
  id: number
  modelId: number
  modelVersion: string
  dataId: number
  modelSerialNo: string
  resultFilterParam: ResultFilterParam
  modelResult: ModelApiResponse | null
}

export interface InferenceResponse {
  modelCode: string | null
  modelDataResults: ModelDataResult[]
}

export interface ModelApiResponse {
  code: string // "OK"
  message: string | null
  dataId: number
  objects: Object2DLine[]
}

interface BaseObject {
  modelClass: string
  classId: number
  type: string
  confidence: number
}

export interface Object3DBox extends BaseObject {
  center3D: Vec3
  rotation3D: Vec3
  size3D: Vec3
}

export interface Object2DLine extends BaseObject {
  points: Vec2[]
}

export interface Vec3 {
  x: number
  y: number
  z: number
}

export interface Vec2 {
  x: number
  y: number
}
