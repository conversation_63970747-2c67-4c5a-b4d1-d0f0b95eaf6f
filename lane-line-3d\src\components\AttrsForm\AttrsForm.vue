<script setup lang="ts">
import { computed } from 'vue'

import { type IAttr, isAttrVisible } from '@/utils/attrs'

import AttrsFormItem from './AttrsFormItem.vue'

// ***************Props and Emits***************
const props = withDefaults(
  defineProps<{
    attrs?: IAttr[]
    data?: Record<string, any>
  }>(),
  {
    attrs: () => [],
    data: () => ({}),
  },
)
const emit = defineEmits<{
  change: [Record<string, any>, { key: string; value: any }[]]
}>()
// *********************************************

const attrMap = computed(() => {
  const map = {} as Record<string, IAttr>
  props.attrs.forEach((attr) => {
    map[attr.id] = attr
  })
  return map
})

function handleChange(key: string, value: any) {
  emit(
    'change',
    {
      ...props.data,
      [key]: value,
    },
    [{ key, value }],
  )
}
</script>

<template>
  <div>
    <div class="attr-container">
      <div
        v-for="item in attrs"
        :key="item.id"
        class="attr-item"
      >
        <AttrsFormItem
          v-if="isAttrVisible(item, attrMap, data)"
          :item="item"
          :model-value="data[item.id]"
          @update:model-value="handleChange(item.id, $event)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.copy-attr {
  position: absolute;
  right: 10px;

  .icon {
    margin-left: 10px;
    font-size: 18px;
    cursor: pointer;
  }
}

.slide-wrap {
  position: relative;
  padding-left: 5px;
  margin-top: 10px;

  .ant-slider {
    display: inline-block;
    width: 130px;
    margin: 0;
    vertical-align: middle;
  }

  .title {
    vertical-align: middle;
    color: inherit;
  }
}
</style>
