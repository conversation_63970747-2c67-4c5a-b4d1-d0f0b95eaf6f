<script setup lang="ts">
import { type IAttr } from '@/utils/attrs'

import { Types } from './components'

// ***************Props and Emits***************
const emit = defineEmits(['change', 'update:value'])
defineProps<{
  item: IAttr
  disabled?: boolean
}>()
const value = defineModel<any>()
// *********************************************

function onChange(...args: any[]) {
  emit('change', ...args)
}
</script>

<template>
  <div
    class="name"
    :span="10"
  >
    {{ item.label || item.name }}
  </div>
  <div
    class="value"
    :span="14"
  >
    <component
      :is="Types[item.type]"
      :disabled="disabled"
      :name="item.id"
      :options="item.options"
      :value="value"
      @change="onChange"
      @update:value="value = $event"
    />
  </div>
</template>

<style lang="less"></style>
