<script setup lang="ts">
import { ref, watch } from 'vue'

export interface IOption {
  value: string
  label: string
}

export interface IProps {
  options: IOption[]
  value: string[]
  name: string
}

// ***************Props and Emits***************
const emit = defineEmits(['update:value', 'change'])
const props = defineProps<IProps>()
// *********************************************

const value = ref(props.value || [])
watch(
  () => props.value,
  () => {
    if (value.value !== props.value) value.value = props.value as any
  },
)

function onChange() {
  emit('update:value', value.value)
  emit('change', value.value)
}
</script>
<template>
  <a-checkbox-group
    v-model:value="value"
    :options="props.options"
    size="small"
    v-bind="$attrs"
    @change="onChange"
  >
    <!-- <a-radio v-for="item in props.options" :value="item.value">{{ item.value }}</a-radio> -->
  </a-checkbox-group>
</template>

<style lang="less"></style>
