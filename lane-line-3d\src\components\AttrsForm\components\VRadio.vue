<script setup lang="ts">
import { ref, watch } from 'vue'

export interface IOption {
  value: string
  label: string
}

export interface IProps {
  options: IOption[]
  value: string
  name: string
}

// ***************Props and Emits***************
const emit = defineEmits(['update:value', 'change'])
const props = defineProps<IProps>()
// *********************************************

const value = ref(props.value || '')
watch(
  () => props.value,
  () => {
    if (value.value !== props.value) value.value = props.value as any
  },
)

function onChange() {
  emit('update:value', value.value)
  emit('change', value.value)
}
</script>
<template>
  <a-radio-group
    v-model:value="value"
    size="small"
    v-bind="$attrs"
    @change="onChange"
  >
    <a-radio
      v-for="item in props.options"
      :key="item.value"
      :value="item.value"
    >
      {{ item.label || item.value }}
    </a-radio>
  </a-radio-group>
</template>

<style lang="less"></style>
