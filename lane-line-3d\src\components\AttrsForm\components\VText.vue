<script setup lang="ts">
import { ref, watch } from 'vue'

// ***************Props and Emits***************
const emit = defineEmits(['update:value', 'change'])
const props = defineProps<{
  value: string
  name: string
}>()
// *********************************************

const value = ref(props.value || '')
watch(
  () => props.value,
  () => {
    if (value.value !== props.value) value.value = props.value as any
  },
)

function onChange() {
  emit('update:value', value.value)
  emit('change', value.value)
}
</script>
<template>
  <a-input
    v-bind="$attrs"
    v-model:value="value"
    placeholder=""
    size="small"
    style="width: 220px"
    @change="onChange"
  />
</template>

<style lang="less"></style>
