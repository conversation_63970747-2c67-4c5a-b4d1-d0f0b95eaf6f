<script lang="ts" setup>
import * as THREE from 'three'
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { onMounted, onScopeDispose, useTemplateRef, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    parent: THREE.Object3D
    position: [number, number, number]
  }>(),
  {
    position: () => [0, 0, 0],
  },
)

const domRef = useTemplateRef('dom')

onMounted(() => {
  const obj = new CSS2DObject(domRef.value!)

  watch(
    () => props.position,
    (position) => {
      const [x, y, z] = position
      obj.position.set(x, y, z)
    },
    { immediate: true },
  )
  props.parent.add(obj)
  onScopeDispose(() => {
    obj.removeFromParent()
  })
})
</script>

<template>
  <div ref="dom">
    <slot />
  </div>
</template>
