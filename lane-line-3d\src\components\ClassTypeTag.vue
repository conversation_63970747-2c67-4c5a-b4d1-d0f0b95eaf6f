<script setup lang="ts">
import { computed } from 'vue'

import type { IClassType } from '@/utils/classType'

const props = defineProps<{
  data: IClassType
  active?: boolean
}>()

const style = computed(() => {
  return props.active
    ? {
        color: '#fff',
        borderColor: '#177ddc',
        backgroundColor: '#177ddc',
        cursor: 'default',
      }
    : {
        color: props.data.color,
        borderColor: props.data.color,
        backgroundColor: 'transparent',
        cursor: 'pointer',
      }
})
</script>

<template>
  <a-tag
    class="class-type-tag min-w-12 text-center"
    :style="style"
  >
    <i
      class="iconfont icon-lifangti"
      style="font-size: 14px"
    />
    {{ data.label }}
  </a-tag>
</template>

<style lang="scss" scoped>
.class-type-tag {
  margin: 0;
}
</style>
