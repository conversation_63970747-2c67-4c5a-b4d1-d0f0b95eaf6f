<template>
  <circle
    ref="circle"
    class="pointer-events-auto hover:scale-125"
    :class="{
      'cursor-move': draggable,
      'cursor-pointer': !draggable,
    }"
    :cx="point[0]"
    :cy="point[1]"
    :fill="active ? '#ff0000' : '#fff'"
    r="4"
    stroke="#2c8fff"
    stroke-width="2"
    :style="{
      'transform-origin': `${point[0]}px ${point[1]}px`,
    }"
  />
</template>

<script setup lang="ts">
import { templateRef, useDraggable } from '@vueuse/core'

const props = defineProps<{
  point: [number, number]
  active?: boolean
  draggable?: boolean
}>()
const emit = defineEmits(['move', 'moveStart', 'moveEnd'])

const elRef = templateRef<SVGCircleElement | null>('circle')
useDraggable(elRef, {
  disabled: () => !props.draggable,
  onStart: () => {
    emit('moveStart')
  },
  onMove: (e) => {
    const rect = elRef.value!.getBoundingClientRect()
    emit('move', {
      x: e.x + rect.width / 2,
      y: e.y + rect.height / 2,
    })
  },
  onEnd: () => {
    emit('moveEnd')
  },
})
</script>
