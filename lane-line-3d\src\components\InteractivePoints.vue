<template>
  <svg
    class="pointer-events-none absolute h-full w-full"
    xmlns="http://www.w3.org/2000/svg"
  >
    <line
      v-for="index in lines.length - 1"
      :key="index"
      class="line pointer-events-auto"
      stroke="#ff0000"
      stroke-width="2"
      :x1="lines[index - 1][0]"
      :x2="lines[index][0]"
      :y1="lines[index - 1][1]"
      :y2="lines[index][1]"
      @click="$emit('clickLine', $event, index - 1)"
    />
    <InteractiveCircle
      v-for="(point, index) in points"
      :key="index"
      :active="index === activeIndex"
      :point="point"
      @click="activeIndex = index"
    />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import InteractiveCircle from '@/components/InteractiveCircle.vue'

const props = defineProps<{
  points: [number, number][]
  close?: boolean
}>()
defineEmits<{
  clickLine: [MouseEvent, number]
}>()

const activeIndex = defineModel<number>()
const lines = computed(() => {
  if (props.points.length <= 1) return []
  const points = [...props.points]
  if (props.close) {
    points.push(points[0])
  }
  return points
})
</script>

<style scoped lang="scss">
.line:hover {
  stroke: #fff;
  stroke-width: 4;
}
</style>
