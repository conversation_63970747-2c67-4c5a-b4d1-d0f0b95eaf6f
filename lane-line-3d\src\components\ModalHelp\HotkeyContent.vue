<template>
  <div class="flex items-center text-sm leading-6">
    <template
      v-for="(key, j) in keys"
      :key="j"
    >
      <span class="mx-1 self-center rounded bg-white px-1.5 text-[#1f1f1f]">
        {{ transform(key) }}
      </span>
      <span v-if="j < keys.length - 1">+</span>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { capitalize } from 'lodash-es'
import { computed } from 'vue'

const props = defineProps<{
  hotkey: string
}>()

const keys = computed(() => props.hotkey.split('+'))

function transform(key: string) {
  switch (key) {
    case 'left':
      return '←'
    case 'up':
      return '↑'
    case 'right':
      return '→'
    case 'down':
      return '↓'
    default:
      return capitalize(key)
  }
}
</script>
