<template>
  <div class="model-help">
    <div class="i-help-body">
      <a-row
        v-for="(group, i) in hotkeyDefinitions.filter((item) => !item.hidden)"
        :key="i"
        :gutter="8"
      >
        <a-col
          class="i-flex i-header"
          :span="24"
        >
          <div>{{ group.title }}</div>
          <div class="i-title-line" />
        </a-col>
        <a-col
          v-for="(item, j) in group.items"
          :key="j"
          :span="12"
        >
          <a-row style="margin: 5px 0">
            <a-col
              class="i-flex"
              :span="10"
            >
              <HotkeyContent :hotkey="item.key" />
            </a-col>
            <a-col :span="13">{{ item.name }}</a-col>
          </a-row>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { hotkeyDefinitions } from '@/editors/LaneLine3D/hotkey'

import HotkeyContent from './HotkeyContent.vue'
</script>
<style lang="scss">
.model-help {
  padding: 0 16px;

  .i-header {
    height: 30px;
    line-height: 30px;
  }

  .i-flex {
    display: flex;

    .i-title-line {
      flex: 1 1 0%;
      align-self: center;
      height: 1px;
      margin: 0 40px 0 8px;
      border-top: 1px solid #515151;
    }
  }

  .i-help-body {
    max-height: 70vh;
    margin-right: -10px;
    margin-left: -10px;
    overflow: hidden auto;
  }
}
</style>
