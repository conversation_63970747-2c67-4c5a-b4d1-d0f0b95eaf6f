<script setup lang="ts">
import { flip, offset, shift, useFloating } from '@floating-ui/vue'
import { computed, ref } from 'vue'

import useEditor from '@/editors/LaneLine3D/useEditor'
import { getAllClassAttrs } from '@/utils/classType'

const props = defineProps<{
  geometryId?: string
  clientX?: number
  clientY?: number
}>()

const editor = useEditor()

const createVirtualElement = (clientX: number, clientY: number) => {
  return {
    getBoundingClientRect() {
      return {
        width: 0,
        height: 0,
        x: clientX,
        y: clientY,
        left: clientX,
        right: clientX,
        top: clientY,
        bottom: clientY,
      }
    },
  }
}

const reference = computed(() => createVirtualElement(props.clientX || 0, props.clientY || 0))
const tooltipEl = ref<HTMLDivElement>()
const { floatingStyles } = useFloating(reference, tooltipEl, {
  middleware: [offset(12), flip(), shift()],
})

const config = computed(() => {
  if (!props.geometryId) {
    return null
  }
  const geometryId = props.geometryId
  const trackObject = editor.utils.getTrackObjectByGeometryId(geometryId)
  const classId = trackObject?.classId
  if (!classId) {
    return null
  }
  const classType = editor.taskData.labels.find((label) => label.id === classId)
  if (!classType) {
    return null
  }

  const geometry = trackObject.geometries.find((item) => item.id === geometryId)
  if (!geometry) {
    return null
  }
  const values = Object.assign({}, trackObject.classData, geometry.classData)
  return {
    classType,
    table: getAllClassAttrs(classType, geometry?.type)
      .filter((attr) => values.hasOwnProperty(attr.id))
      .map((attr) => {
        return {
          label: attr.name,
          value: values[attr.id],
        }
      }),
  }
})
</script>

<template>
  <Teleport to="body">
    <div
      v-show="!!geometryId"
      ref="tooltipEl"
      class="object-tooltip"
      :style="floatingStyles"
    >
      <div v-if="geometryId && !config">无标签</div>
      <template v-else-if="geometryId && config">
        <div class="font-bold text-red-500">{{ config.classType.name }}</div>
        <table>
          <tbody>
            <tr
              v-for="(item, i) in config.table"
              :key="i"
            >
              <td>{{ item.label }}：</td>
              <td class="text-right">{{ item.value }}</td>
            </tr>
          </tbody>
        </table>
      </template>
    </div>
  </Teleport>
</template>

<style lang="less">
.object-tooltip {
  z-index: 999;
  padding: 4px 8px;
  color: rgb(255 255 255 / 85%);
  background: #0008;
  border: 1px solid rgb(255 255 255 / 85%);
  border-radius: 2px;
}
</style>
