<script lang="ts" setup>
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { computed, ref, useTemplateRef } from 'vue'

const props = withDefaults(
  defineProps<{
    resizeSide: 'left' | 'right'
    initialWidth?: number
    minWidth?: number
    maxWidth?: number
  }>(),
  {
    initialWidth: 300,
    minWidth: 100,
    maxWidth: 500,
  },
)

const width = ref(props.initialWidth)
const visible = ref(true)

const containerStyle = computed(() => ({
  width: visible.value ? `${width.value}px` : 0,
  [props.resizeSide === 'left' ? 'right' : 'left']: 0,
}))

const handleRef = useTemplateRef<HTMLElement>('handle')
let startX = 0
let startWidth = 0

function onHandleMousedown(event: MouseEvent) {
  document.body.style.cursor = 'ew-resize'
  const target = event.currentTarget as HTMLElement
  target.classList.add('active')
  startX = event.screenX
  startWidth = width.value

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

function onMouseMove(event: MouseEvent) {
  const deltaX = event.screenX - startX
  let newWidth = props.resizeSide === 'left' ? startWidth - deltaX : startWidth + deltaX

  // 限制宽度范围
  newWidth = Math.max(props.minWidth, Math.min(props.maxWidth, newWidth))

  width.value = newWidth
  visible.value = newWidth > props.minWidth
}

function onMouseUp() {
  document.body.style.cursor = ''
  handleRef.value?.classList.remove('active')
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
}
</script>

<template>
  <div
    class="stretchable-panel"
    :class="resizeSide"
    :style="containerStyle"
  >
    <div
      v-show="visible"
      class="h-full w-full"
    >
      <slot />
    </div>
    <div
      v-show="visible"
      ref="handle"
      :class="['handle-line', resizeSide]"
      @mousedown="onHandleMousedown"
    />
    <div
      :class="['visible-handle', resizeSide]"
      @click="visible = !visible"
    >
      <template v-if="resizeSide === 'right'">
        <LeftOutlined v-if="visible" />
        <RightOutlined v-else />
      </template>
      <template v-else>
        <RightOutlined v-if="visible" />
        <LeftOutlined v-else />
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.stretchable-panel {
  position: relative;

  &.left {
    border-left: 3px solid transparent;
  }

  &.right {
    border-right: 3px solid transparent;
  }
}

.handle-line {
  position: absolute;
  top: 0;
  width: 3px;
  height: 100%;
  background-color: transparent;
  transition: background-color 0.2s;

  &.left {
    left: -3px;
  }

  &.right {
    right: -3px;
  }

  &.active {
    background-color: #2e8cf0;
  }

  &:hover {
    cursor: ew-resize;
    background-color: #2e8cf0;
  }
}

.visible-handle {
  position: absolute;
  top: 50%;
  z-index: 1;
  width: 18px;
  padding: 10px 0;
  line-height: 24px;
  color: #aaa;
  text-align: center;
  cursor: pointer;
  background: #3a393e;
  transform: translateY(-50%);
  transition: background-color 0.2s;

  &:hover {
    background: #2b2a2e;
  }

  &.left {
    left: 0;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    transform: translate(-100%, -50%);
  }

  &.right {
    right: 0;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    transform: translate(100%, -50%);
  }
}
</style>
