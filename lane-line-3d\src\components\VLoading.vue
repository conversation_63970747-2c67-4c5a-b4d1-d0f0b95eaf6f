<script setup lang="ts">
import { LoadingOutlined } from '@ant-design/icons-vue'

defineProps<{ bg?: boolean; text?: string }>()
</script>

<template>
  <div
    class="flex items-center justify-center gap-3"
    :class="{ 'inset-0 bg-[#333333e6]': bg }"
  >
    <LoadingOutlined class="text-xl text-blue-500" />
    <span
      v-if="text"
      class="text-xs text-[#a2a2a2]"
    >
      {{ text }}
    </span>
  </div>
</template>
