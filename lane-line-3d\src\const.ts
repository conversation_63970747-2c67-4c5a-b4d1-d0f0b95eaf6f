export enum GeometryTypes {
  Point3D = '3D_KEY_POINT',
  Polyline3D = '3D_POLYLINE',
  Polygon3D = '3D_POLYGON',

  MultiRect2D = '2D_MULTI_RECT',

  Box3D = '3D_BOX',
}

export const ViewEvent = {
  RENDER_BEFORE: 'render_before',
  RENDER_AFTER: 'render_after',

  OBJECT_CLICK: 'object_click',
  OBJECT_DBLCLICK: 'object_dblclick',
  OBJECT_HOVER: 'object_hover',

  VISIBLE_CHANGE: 'visible_change',
  OPTIONS_CHANGE: 'options_change',
  ACTIVE_POINT_CHANGE: 'active_point_change',
}

export enum SourceType {
  TASK = 'TASK',
  DATA_FLOW = 'DATA_FLOW',
  MODEL = 'MODEL',
}
