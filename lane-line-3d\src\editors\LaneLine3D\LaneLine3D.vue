<script setup lang="ts">
import { CloseCircleOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'

import StretchablePanel from '@/components/StretchablePanel/StretchablePanel.vue'
import VLoading from '@/components/VLoading.vue'
import AnnotationInfo from '@/layouts/AnnotationInfo/AnnotationInfo.vue'
import FrameActionBar from '@/layouts/FrameActionBar/FrameActionBar.vue'
import OperationPanel from '@/layouts/OperationPanel/OperationPanel.vue'
import TheHeader from '@/layouts/TheHeader/TheHeader.vue'
import TheToolbar from '@/layouts/TheToolbar/TheToolbar.vue'
import useSelection from '@/stores/useSelection'
import ImageListPreviewView from '@/views/ImageView/ImageListPreviewView.vue'
import ImageView from '@/views/ImageView/ImageView.vue'
import HeightRangeControl from '@/views/PointCloudMainView/components/HeightRangeControl.vue'
import MotionTrackOverlay from '@/views/PointCloudMainView/components/MotionTrackOverlay.vue'
import ObjectTooltip from '@/views/PointCloudMainView/components/ObjectTooltip.vue'
import RenderObject from '@/views/PointCloudMainView/components/RenderObject.vue'
import ViewHelper from '@/views/PointCloudMainView/components/ViewHelper/ViewHelper.vue'
import PointCloudMainView from '@/views/PointCloudMainView/PointCloudMainView.vue'
import PointCloudThreeView from '@/views/PointCloudSideView/PointCloudThreeView.vue'

import useEditor from './useEditor'
import useFusionTrack from './useFusionTrack'

const editor = useEditor()
const fusionTrack = useFusionTrack(() => !editor.taskData.isSeriesFrame && editor.status.isReady)

const activeImageData = ref<any>()

const pointCloudObject3D = editor.pointCloud.object
const geometriesObject3D = editor.geometryGroup.object
const { onlySelectedGeometryObject } = useSelection()
</script>

<template>
  <div class="flex h-full flex-col">
    <TheHeader />
    <div class="relative flex flex-1 overflow-hidden pt-1">
      <div class="flex flex-1 flex-col">
        <div class="flex flex-1">
          <div class="relative flex flex-1">
            <template v-if="!activeImageData">
              <StretchablePanel
                :initial-width="230"
                resize-side="right"
              >
                <ImageListPreviewView
                  :config-url="fusionTrack.activeResource?.configUrl"
                  :image-urls="fusionTrack.activeResource?.imageUrls"
                  :pose="fusionTrack.globalPoses[fusionTrack.activeIndex]"
                  @dblclick-item="activeImageData = $event"
                />
              </StretchablePanel>
            </template>
            <PointCloudMainView
              ref="pointCloudView"
              class="z-0 flex-1"
            >
              <template #default>
                <HeightRangeControl
                  class="pointer-events-auto absolute left-4 top-4 z-10 text-xs text-gray-300"
                />
                <MotionTrackOverlay
                  v-if="editor.userConfig.showTrack && !editor.taskData.isSeriesFrame"
                  v-model="fusionTrack.activeIndex"
                  :poses="fusionTrack.globalPoses"
                />
                <ViewHelper />
                <ObjectTooltip />
                <RenderObject :object="pointCloudObject3D" />
                <RenderObject :object="geometriesObject3D" />
                <AnnotationInfo
                  class="absolute bottom-5 right-0 z-50 max-h-[min(600px,60vh)] w-96"
                />
              </template>
            </PointCloudMainView>
            <template v-if="activeImageData">
              <div class="absolute left-0 z-10 h-full w-full bg-black">
                <ImageView v-bind="activeImageData" />
                <span
                  class="absolute right-1.5 top-1.5 z-10 h-7 w-7 cursor-pointer rounded bg-[#333] text-center text-lg text-white"
                  @click="activeImageData = null"
                >
                  <CloseCircleOutlined />
                </span>
              </div>
            </template>
          </div>
          <StretchablePanel resize-side="left">
            <PointCloudThreeView
              :focused-target="onlySelectedGeometryObject"
              :objects="onlySelectedGeometryObject ? [onlySelectedGeometryObject] : []"
              :point-cloud="pointCloudObject3D"
            />
          </StretchablePanel>
        </div>
        <FrameActionBar
          v-if="editor.taskData.isSeriesFrame"
          v-model="editor.activeFrameIndex"
          :frames="editor.taskData.frames"
        />
      </div>
      <TheToolbar />
      <OperationPanel class="w-72 overflow-auto" />
      <VLoading
        v-if="editor.status.isLoading || editor.pointCloud.loading"
        bg
        class="absolute inset-0"
        :text="
          editor.pointCloud.loading
            ? `加载点云${(editor.pointCloud.progress * 100).toFixed(2)}%`
            : ''
        "
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
