import UAParser from 'ua-parser-js'

import type { IHotkeyConfig } from '@/hooks/useHotkey'

interface IHotkeyDefinition {
  title?: string
  hidden?: boolean
  items: Array<IHotkeyConfig & { name: string }>
}

const parser = new UAParser()
const osInfo = parser.getResult()
const osName = (osInfo.os.name || '').toLowerCase()
const isMac = osName.indexOf('mac') >= 0

export const hotkeyDefinitions: IHotkeyDefinition[] = [
  {
    title: '标注',
    items: [
      { key: isMac ? '⌘+z' : 'ctrl+z', action: 'undo', name: '撤销' },
      { key: isMac ? '⌘+shift+z' : 'ctrl+shift+z', action: 'redo', name: '重做' },
      { key: isMac ? 'backspace' : 'del', action: 'del', name: '删除几何体' },
      { key: 'h', action: 'unselectAll', name: '取消选择' },
    ],
  },
  {
    title: '显示',
    items: [
      { key: 't', action: 'toggleAnnotationInfoVisible', name: '展示 / 隐藏标注信息' },
      { key: 'm', action: 'toggleAnnotationLabelVisible', name: '展示 / 隐藏标注标签' },
      // { key: 'b', action: 'filterProjectionBySection', name: '展示 / 隐藏选中物体外的2D投影' },
      { key: 'k', action: 'toggleTrackVisible', name: '展示 / 隐藏车辆轨迹' },
    ],
  },
  {
    title: '其他',
    items: [
      { key: isMac ? '⌘+s' : 'ctrl+s', action: 'save', name: '保存' },
      { key: 'right', action: 'right', name: '下一帧' },
      { key: 'left', action: 'left', name: '上一帧' },
    ],
  },
  {
    hidden: true,
    items: [
      { key: 'esc', action: 'esc', name: 'esc' },
      { key: 'enter', action: 'enter', name: 'enter' },
    ],
  },
]

export const hotkeyConfigs = hotkeyDefinitions.flatMap((item) => item.items)
