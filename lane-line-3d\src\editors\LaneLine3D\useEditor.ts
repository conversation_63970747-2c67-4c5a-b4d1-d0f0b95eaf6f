import { createGlobalState } from '@vueuse/core'
import { message } from 'ant-design-vue'
import mitt from 'mitt'
import * as THREE from 'three'
import { computed, markRaw, reactive, ref, shallowReactive, unref, watch } from 'vue'
import type { JSX } from 'vue/jsx-runtime'

import { getAnnotationData, saveAnnotationData } from '@/api/annotation'
import { getClassTypes, getFrameFileConfig2, getTaskFormAttrs, getTaskInfo } from '@/api/common'
import { GeometryTypes } from '@/const'
import useGeometry2DList from '@/hooks/useGeometry2DList'
import useGeometryGroup from '@/hooks/useGeometryGroup'
import { useHistory } from '@/hooks/useHistory'
import useHotkey from '@/hooks/useHotkey'
import usePointCloud from '@/hooks/usePointCloud'
import Box3D, { type IBox3D } from '@/objects/Box3D'
import MultiRect2D, { type IMultiRect2D } from '@/objects/MutiRect2D'
import Point3D, { type IPoint3D, setupPoint3DFeature } from '@/objects/Point3D'
import Polygon3D, { type IPolygon3D, setupPolygon3DFeature } from '@/objects/Polygon3D'
import Polyline3D, { type IPolyline3D, setupPolyline3DFeature } from '@/objects/Polyline3D'
import useActionManager from '@/stores/useActionManager'
import useInspection from '@/stores/useInspection'
import useInteractionManager from '@/stores/useInteractionManager'
import useSaveDataChangeTracker from '@/stores/useSaveDataChangeTracker'
import useSelection from '@/stores/useSelection'
import useTaskId from '@/stores/useTaskId'
import type { Optional } from '@/types'
import { type IClassification } from '@/utils/classification'
import {
  getDefaultCommonClassData,
  getDefaultPrivateClassData,
  type IClassType,
} from '@/utils/classType'
import { remoteToLocalTrackObjects } from '@/utils/convertObjects'
import type BaseViewController from '@/views/BaseViewController'
import useHeightMap from '@/views/PointCloudMainView/hooks/useHeightMap'

import { hotkeyConfigs } from './hotkey'

export enum EditorEvent {
  ViewMounted = 'view-mounted',
}

const Geometry3DRegistry = {
  [GeometryTypes.Point3D]: Point3D,
  [GeometryTypes.Polyline3D]: Polyline3D,
  [GeometryTypes.Polygon3D]: Polygon3D,
  [GeometryTypes.Box3D]: Box3D,
}

export const Geometry2DRegistry = {
  [GeometryTypes.MultiRect2D]: MultiRect2D,
}

export type IGeometry =
  | IPoint3D<GeometryTypes.Point3D>
  | IPolyline3D<GeometryTypes.Polyline3D>
  | IPolygon3D<GeometryTypes.Polygon3D>
  | IBox3D<GeometryTypes.Box3D>
  | IMultiRect2D<GeometryTypes.MultiRect2D>

export interface ITrackObject {
  /** trackId */
  id: string
  name?: string
  classId?: number | string
  /** 公共属性的表单值，表单与 classId 有关 */
  classData?: Record<string, any>
  /** 至少包含一个 */
  geometries: IGeometry[]
}

export interface IFrame {
  id: string
  name: string
  loaded: boolean
  pointCloudUrl: string
  /**
   * 一般项目一帧包含一份 resource。
   * 点云融合项目只有一帧，该帧会包含融合前所有帧的 resource。
   */
  frameResources: {
    configUrl: string
    imageUrls: string[]
    poseUrl?: string
  }[]
  heightMapUrl: string
  heightMapMetaUrl: string
}

export interface IFrameData {
  frameId: string | number
  classificationData: Record<string, any>
}

export interface ITaskData {
  taskId: string
  isSeriesFrame: boolean
  frames: IFrame[]
  labels: IClassType[]
  classifications: IClassification[]
}

export const useEditor = createGlobalState(() => {
  const userConfig = ref({
    showTrack: true,
  })

  const { taskId } = useTaskId()

  const taskData = ref<ITaskData>({
    taskId,
    isSeriesFrame: false,
    frames: [],
    labels: [],
    classifications: [],
  })

  /** 任务标注数据，包括标注物体和帧属性 */
  const trackObjects = ref<ITrackObject[]>([])
  const framesData = ref<IFrameData[]>([])

  const activeFrameIndex = ref(0)
  const activeFrame = computed(
    () =>
      unref(taskData).frames[activeFrameIndex.value] || {
        id: '',
        name: '',
        loaded: false,
        frameResources: [],
        pointCloudUrl: '',
        heightMapUrl: '',
        heightMapMetaUrl: '',
      },
  )

  const trackObjectsHistory = useHistory(trackObjects)
  const applyChanges = trackObjectsHistory.commit

  const status = reactive({
    isReady: false,
    isLoading: false,
    isSaving: false,
    isSubmitting: false,
    isPendingClose: false,
  })

  const interactionManager = useInteractionManager()

  const toolbar = shallowReactive<Record<string, () => JSX.Element>>({})

  /** 通用的，不跟特定几何类型耦合的工具函数 */
  const utils = {
    getTrackObjectByGeometryId(id: string) {
      return (
        unref(trackObjects).find((item) => item.geometries.find((item) => item.id === id)) || null
      )
    },
    deleteTrackObjectById: (...ids: string[]) => {
      ids = [...ids]
      applyChanges((draft) => {
        for (let i = draft.length - 1; i >= 0 && ids.length > 0; i--) {
          const trackObject = draft[i]
          const index = ids.indexOf(trackObject.id)
          if (index !== -1) {
            draft.splice(i, 1)
            ids.splice(index, 1)
          }
        }
      })
    },
    deleteGeometryById: (...ids: string[]) => {
      ids = [...ids]
      applyChanges((draft) => {
        for (let i = draft.length - 1; i >= 0 && ids.length > 0; i--) {
          const trackObject = draft[i]
          for (let j = trackObject.geometries.length - 1; j >= 0 && ids.length > 0; j--) {
            const geometry = trackObject.geometries[j]
            const index = ids.indexOf(geometry.id)
            if (index !== -1) {
              trackObject.geometries.splice(j, 1)
              ids.splice(index, 1)
              if (trackObject.geometries.length === 0) {
                draft.splice(i, 1)
              }
            }
          }
        }
      })
    },
    updateTrackObjectById(id: string, cb: (trackObject: ITrackObject) => void) {
      applyChanges((draft) => {
        const trackObject = draft.find((item) => item.id === id)
        if (trackObject) {
          cb(trackObject)
        }
      })
    },
    updateGeometryById(id: string, cb: (geometry: IGeometry) => void) {
      applyChanges((draft) => {
        for (const trackObject of draft) {
          for (const geometry of trackObject.geometries) {
            if (geometry.id === id) {
              cb(geometry)
              return
            }
          }
        }
      })
    },
    createGeometryInInteraction(data: Optional<IGeometry, 'id' | 'frameId'>) {
      const selection = useSelection()
      const inspection = useInspection()
      let geometry: IGeometry
      const data2 = {
        frameId: activeFrame.value.id,
        ...data,
      }
      if (selection.selectedTrackObjects.value.length === 1) {
        const trackId = selection.selectedTrackObjects.value[0].id
        geometry = utils.createGeometryInTrackObject(data2, trackId)
      } else {
        geometry = utils.createGeometry(data2)
      }
      selection.selectGeometryById(geometry.id)
      inspection.isInspecting.value = true
      return geometry
    },
    createGeometryInTrackObject(data: Optional<IGeometry, 'id'>, trackId: string) {
      const geometry = {
        id: THREE.MathUtils.generateUUID(),
        ...data,
      }
      applyChanges((draft) => {
        const trackObject = draft.find((item) => item.id === trackId)
        if (trackObject) {
          if (trackObject.classId && !geometry.classData) {
            geometry.classData = getDefaultPrivateClassData(
              unref(taskData).labels.find((item) => item.id === trackObject.classId),
              data.type,
            )
          }
          trackObject.geometries.push(geometry)
        }
      })
      return geometry
    },
    createGeometry(data: Optional<IGeometry, 'id'>, classId?: number | string) {
      const geometry = {
        id: THREE.MathUtils.generateUUID(),
        classData: getDefaultPrivateClassData(
          unref(taskData).labels.find((item) => item.id === classId),
          data.type,
        ),
        ...data,
      }
      applyChanges((draft) => {
        let maxIndex = Math.max(
          0,
          ...draft.map((item) => parseInt(item.name || '0')).filter((n) => !isNaN(n)),
        )
        draft.push({
          id: THREE.MathUtils.generateUUID(),
          name: `${++maxIndex}`,
          geometries: [geometry],
          classId,
          classData: getDefaultCommonClassData(
            unref(taskData).labels.find((item) => item.id === classId),
          ),
        })
      })
      return geometry
    },
  }

  const methods = {
    async save(options?: { quiet?: boolean }) {
      const { quiet = false } = options || {}
      status.isSaving = true
      const saveDataChangeTracker = useSaveDataChangeTracker()
      try {
        const { dataInfos, updatedGeometryIds } = saveDataChangeTracker.getChangedFrames()

        if (dataInfos.length === 0) {
          if (!quiet) {
            message.info('没有需要保存的数据')
          }
          return
        }

        const data = await saveAnnotationData({
          datasetId: '1',
          dataInfos,
        })
        const keyMap = {} as Record<string, Record<string, string | number>>
        data.forEach((e) => {
          const dataId = e.dataId
          keyMap[dataId] = keyMap[dataId] || {}
          keyMap[dataId][e.frontId] = e.id
        })
        // 注意：
        // 这里没有使用 applyCommit，而是直接修改了原始数据，
        // 是为了避免撤销对新建对象的修改（非创建）后再保存导致因丢失 backId 重复创建的物体，
        // 现在这么做说不定会有问题（撤销/重做层面），
        // 另一个选择是直接使用 applyCommit，因为后端会删除旧的创建一个新的。
        Object.keys(keyMap).forEach((dataId) => {
          const dataKeyMap = keyMap[dataId]
          trackObjects.value.forEach((trackObject) => {
            trackObject.geometries.forEach((geo) => {
              if (geo.frameId !== dataId) return
              const frontId = geo.id
              if (updatedGeometryIds.includes(frontId)) {
                geo.version = (geo.version || 1) + 1
              }
              const backId = dataKeyMap[frontId]
              if (!backId) return
              geo.backId = backId
            })
          })
        })
        saveDataChangeTracker.updateSnapshots()
        if (!quiet) {
          message.success('保存成功')
        }
      } finally {
        status.isSaving = false
      }
    },
    undo: trackObjectsHistory.undo,
    redo: trackObjectsHistory.redo,
    /**
     * 删除所有选中的几何体，
     * 不包含几何体的标注对象会被同时移除。
     */
    deleteSelectedGeometry() {
      const ids = geometries.value.filter((item) => item.selected).map((item) => item.id)
      utils.deleteGeometryById(...ids)
    },
    toNextFrame() {
      activeFrameIndex.value = (activeFrameIndex.value + 1) % taskData.value.frames.length
    },
    toPrevFrame() {
      const count = taskData.value.frames.length
      activeFrameIndex.value = (activeFrameIndex.value + count - 1) % count
    },
    filterProjectionBySection() {
      console.log('filterProjectionBySection')
    },
    toggleTrackVisible() {
      userConfig.value.showTrack = !userConfig.value.showTrack
    },
    toggleAnnotationInfoVisible() {
      const { isInspecting } = useInspection()
      isInspecting.value = !isInspecting.value
    },
    unselectAll() {
      applyChanges((draft) => {
        const geometries = draft.map((item) => item.geometries).flat()
        geometries.forEach((item) => {
          if (item.selected) {
            item.selected = false
          }
        })
      })
    },
  }

  const eventBus =
    markRaw(
      mitt<{
        [EditorEvent.ViewMounted]: { controller: BaseViewController }
      }>(),
    )

  const actionManager = useActionManager()
  const actionMap = {
    undo: methods.undo,
    redo: methods.redo,
    del: methods.deleteSelectedGeometry,
    right: methods.toNextFrame,
    left: methods.toPrevFrame,
    esc: () => {
      console.log('esc')
    },
    enter: () => {
      console.log('enter')
    },
    save: () => methods.save(),
  }
  Object.entries(actionMap).forEach(([name, value]) => {
    actionManager.useRegisterAction(name, value)
  })

  useHotkey(hotkeyConfigs, {
    ...Object.fromEntries(
      Object.keys(actionMap).map((key) => [key, () => actionManager.triggerAction(key)]),
    ),
    filterProjectionBySection: methods.filterProjectionBySection,
    toggleTrackVisible: methods.toggleTrackVisible,
    toggleAnnotationInfoVisible: methods.toggleAnnotationInfoVisible,
    unselectAll: methods.unselectAll,
  })

  const pointCloud = usePointCloud(() => activeFrame.value?.pointCloudUrl || '')
  const heightMap = useHeightMap(() => {
    return {
      heightMapUrl: activeFrame.value.heightMapUrl,
      heightMapMetaUrl: activeFrame.value.heightMapMetaUrl,
    }
  })
  const geometries = computed(() =>
    unref(trackObjects)
      .map((item) => item.geometries)
      .flat()
      .filter((item) => item.frameId == activeFrame.value.id),
  )
  const geometryGroup = useGeometryGroup(geometries, Geometry3DRegistry)
  const geometry2DList = useGeometry2DList(geometries, Geometry2DRegistry)

  async function init() {
    status.isLoading = true
    try {
      const { dataInfos, isSeriesFrame } = await getTaskInfo()
      taskData.value.isSeriesFrame = isSeriesFrame
      taskData.value.frames = dataInfos.map((item) => {
        return {
          id: item.id,
          name: item.name,
          loaded: false,
          frameResources: [],
          pointCloudUrl: '',
          heightMapUrl: '',
          heightMapMetaUrl: '',
        }
      })
      await Promise.all([
        loadFrame(activeFrame.value.id),
        loadAnnotationsAndLabels(...taskData.value.frames.map((item) => item.id)),
        loadClassifications(),
      ])
      useSaveDataChangeTracker().updateSnapshots()
      status.isReady = true
    } finally {
      status.isLoading = false
    }
  }
  async function loadFrame(frameId: string) {
    const data = await getFrameFileConfig2(frameId)
    const frame = activeFrame.value
    frame.frameResources = data.frameResources!
    frame.pointCloudUrl = data.pointCloudUrl
    frame.heightMapUrl = data.heightMapUrl
    frame.heightMapMetaUrl = data.heightMapMetaUrl
    frame.loaded = true
  }
  async function loadAnnotationsAndLabels(...frameId: string[]) {
    const [data, labels] = await Promise.all([getAnnotationData(frameId), getClassTypes()])
    trackObjects.value = remoteToLocalTrackObjects(
      data.flatMap((item) => item.objects),
      labels,
    )
    framesData.value = data.map((item) => {
      return {
        frameId: item.dataId,
        classificationData: item.classificationData,
      }
    })
    taskData.value.labels = labels
  }
  async function loadClassifications() {
    const classifications = await getTaskFormAttrs()
    taskData.value.classifications = classifications
  }

  init()

  watch(activeFrameIndex, (index) => {
    const frame = taskData.value.frames[index]
    if (!frame || frame.loaded) return
    loadFrame(frame.id)
  })

  const editor = reactive({
    status,

    userConfig,

    taskData,
    activeFrameIndex,
    activeFrame,

    trackObjects,
    framesData,

    geometries,

    methods,
    utils,
    applyChanges,

    pointCloud,
    heightMap,
    geometryGroup,
    geometry2DList,

    interactionManager,
    toolbar,

    on: eventBus.on,
    off: eventBus.off,
    emit: eventBus.emit,
  })

  setupPolyline3DFeature(editor)
  setupPolygon3DFeature(editor)
  setupPoint3DFeature(editor)

  return editor
})

export default useEditor
