import { useAsyncState, whenever } from '@vueuse/core'
import {
  computed,
  effectScope,
  type MaybeRefOrGetter,
  onWatcherCleanup,
  reactive,
  ref,
  toValue,
  unref,
} from 'vue'

import { getGlobalPoses } from '@/api/common'
import useActionManager from '@/stores/useActionManager'

import useEditor from './useEditor'

export default function useFusionTrack(enabled: MaybeRefOrGetter<boolean>) {
  const editor = useEditor()
  const activeIndex = ref(0)
  const activeResource = computed(
    () => unref(editor.activeFrame)?.frameResources[activeIndex.value],
  )
  whenever(
    () => toValue(enabled),
    () => {
      const scope = effectScope()
      scope.run(() => {
        const actinManager = useActionManager()
        actinManager.useRegisterAction('left', () => {
          const count = editor.activeFrame.frameResources.length
          activeIndex.value = (activeIndex.value + count - 1) % count
        })
        actinManager.useRegisterAction('right', () => {
          activeIndex.value = (activeIndex.value + 1) % editor.activeFrame.frameResources.length
        })
        loadGlobalPoses()
      })
      onWatcherCleanup(() => scope.stop())
    },
  )

  const { state: globalPoses, execute: loadGlobalPoses } = useAsyncState(
    () => getGlobalPoses(),
    [],
    {
      immediate: false,
    },
  )
  return reactive({
    activeIndex,
    activeResource,
    globalPoses,
  })
}
