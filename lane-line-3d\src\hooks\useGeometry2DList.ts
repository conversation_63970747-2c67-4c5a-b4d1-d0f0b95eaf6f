import { pull } from 'lodash-es'
import { markRaw, type MaybeRefOrGetter, reactive, toValue } from 'vue'

import useInstances from '@/hooks/useInstances'
import type Object2D from '@/objects/base/Object2D'

export default function useGeometry2DList(
  objects: MaybeRefOrGetter<any[]>,
  ObjectRegistry: Record<string, new (item: any) => Object2D>,
) {
  const list: Object2D[] = reactive([])
  useInstances(() => toValue(objects), 'id', {
    create: (item) => {
      const ObjectClass = ObjectRegistry[item.type]
      if (!ObjectClass) {
        return null
      }
      const instance = markRaw(new ObjectClass(item))
      list.push(instance)
      return instance
    },
    update: (instance, item, oldItem) => {
      if (item.type !== oldItem.type) {
        return true
      }
      instance.update(item)
      instance.dispatchEvent({ type: 'change' })
    },
    destroy: (instance) => pull(list, instance),
  })
  return {
    list,
  }
}
