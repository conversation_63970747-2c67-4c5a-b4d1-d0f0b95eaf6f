import * as THREE from 'three'
import { markRaw, type MaybeRefOrGetter, toValue } from 'vue'

import useInstances from '@/hooks/useInstances'

export default function useGeometryGroup(
  objects: MaybeRefOrGetter<any[]>,
  ObjectRegistry: Record<
    string,
    new (item: any) => THREE.Object3D & { update: (item: any) => void }
  >,
) {
  const group = new THREE.Group()
  const onUpdate = () => group.dispatchEvent({ type: 'change' })
  useInstances(() => toValue(objects), 'id', {
    create: (item) => {
      const ObjectClass = ObjectRegistry[item.type]
      if (!ObjectClass) {
        return null
      }
      const instance = new ObjectClass(item)
      group.add(instance)
      instance.addEventListener('change', onUpdate)
      return instance
    },
    update: (instance, item, oldItem) => {
      if (item.type !== oldItem.type) {
        return true
      }
      instance.update(item)
      instance.dispatchEvent({ type: 'change' })
    },
    destroy: (instance) => instance.removeFromParent(),
    changed: () => {
      onUpdate()
    },
  })
  return {
    object: markRaw(group),
  }
}
