import { apply, create, type Patch } from 'mutative'
import { computed, type Ref, shallowReactive, toRaw } from 'vue'

interface UseHistoryOptions {
  interval?: number // 合并操作的最小时间间隔
  maxLength?: number // 最大历史记录数量
}

export function useHistory<T extends object>(source: Ref<T>, options: UseHistoryOptions = {}) {
  const { interval = 500, maxLength = 100 } = options

  const undoStack: Patch[][] = shallowReactive([])
  const redoStack: Patch[][] = shallowReactive([])

  let updateTime = 0

  const undo = () => {
    if (undoStack.length === 0) return

    updateTime = 0

    const inversePatches = undoStack.pop()!
    const [next, , redoPatches] = create(
      source.value,
      (draft) => {
        apply(draft, inversePatches)
      },
      {
        enablePatches: true,
      },
    )

    source.value = next
    redoStack.push(redoPatches)
  }

  const redo = () => {
    if (redoStack.length === 0) return

    updateTime = 0

    const patches = redoStack.pop()!
    const [next, , newInverse] = create(
      source.value,
      (draft) => {
        apply(draft, patches)
      },
      {
        enablePatches: true,
      },
    )

    source.value = next
    undoStack.push(newInverse)
  }

  const addHistory = (p: Patch[]) => {
    if (undoStack.length > maxLength) {
      undoStack.shift()
    }
    undoStack.push(p)
    redoStack.length = 0
  }

  /**
   * @param merge 是否允许合并操作
   */
  const commit = (recipe: (draft: T) => void, enableRedo = true, merge = true) => {
    if (!enableRedo) {
      recipe(source.value)
      return
    }
    const [next, , inversePatches] = create(toRaw(source.value), recipe, {
      enablePatches: true,
    })
    if (inversePatches.length === 0) {
      return
    }
    source.value = next
    if (merge && Date.now() - updateTime <= interval) {
      undoStack[undoStack.length - 1] = [...inversePatches, ...undoStack[undoStack.length - 1]]
    } else {
      addHistory(inversePatches)
    }
    updateTime = Date.now()
  }
  return {
    undo,
    redo,
    canUndo: computed(() => undoStack.length > 0),
    canRedo: computed(() => redoStack.length > 0),
    commit,
  }
}
