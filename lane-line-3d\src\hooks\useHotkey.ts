import hotkeys from 'hotkeys-js'
import { type MaybeRefOrGetter, onWatcherCleanup, toValue, watch } from 'vue'

export interface IHotkeyConfig<T extends string = string> {
  key: string
  action: T
  params?: any
}

export default function useHotkey(
  configs: MaybeRefOrGetter<IHotkeyConfig[]>,
  actions: Record<string, (...args: any) => any>,
) {
  const setupActionHotkey = (config: IHotkeyConfig) => {
    hotkeys(config.key, (event) => {
      event.preventDefault()
      actions[config.action]?.(config.params)
    })
  }
  watch(
    () => toValue(configs),
    (values) => {
      values.forEach((config) => {
        setupActionHotkey(config)
      })
      onWatcherCleanup(() => {
        hotkeys.unbind()
      })
    },
    {
      deep: true,
      immediate: true,
    },
  )
}
