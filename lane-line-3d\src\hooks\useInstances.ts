import { type MaybeRefOrGetter, onScopeDispose, toValue, watch, type WatchStopHandle } from 'vue'

export interface InstanceLifecycle<T = Record<string, any>, K = unknown> {
  create: (item: T) => K | null
  /**
   * 返回 true 代表重新创建实例
   */
  update: (instance: K, item: T, oldItem: T) => void | true
  destroy: (instance: K) => void
  changed?: () => void
}

interface InstanceData<P> {
  instance: P
  stopHandle: WatchStopHandle
}

export default function useInstances<T extends Record<string, any>, P, K extends keyof T>(
  source: MaybeRefOrGetter<T[]>,
  idKey: K,
  lifecycle: InstanceLifecycle<T, P>,
) {
  // 使用单一 Map 存储实例和监听器停止函数
  const instanceRegistry = new Map<T[K], InstanceData<P>>()

  const getId = (item: T) => item[idKey]

  // 监听数组项的新增和删除
  watch(
    () => toValue(source).map(getId),
    (newIds, oldIds = []) => {
      const added = newIds.filter((id) => !oldIds.includes(id))
      const removed = oldIds.filter((id) => !newIds.includes(id))

      const items = toValue(source)

      for (const id of added) {
        const item = items.find((item) => getId(item) === id)
        if (item) {
          const instance = lifecycle.create(item)
          if (!instance) continue
          const stopHandle = watchItemById(id)
          instanceRegistry.set(id, {
            instance,
            stopHandle,
          })
        }
      }

      for (const id of removed) {
        const instanceData = instanceRegistry.get(id)
        if (instanceData) {
          disposeInstance(instanceData)
          instanceRegistry.delete(id)
        }
      }

      if (added.length || removed.length) {
        lifecycle.changed?.()
      }
    },
    {
      immediate: true,
    },
  )

  onScopeDispose(() => {
    if (!instanceRegistry.size) return

    // 停止所有监听器并销毁所有实例
    for (const instanceData of instanceRegistry.values()) {
      disposeInstance(instanceData)
    }
    instanceRegistry.clear()
    lifecycle.changed?.()
  })

  function watchItemById(targetId: T[K]) {
    return watch(
      () => toValue(source).find((item) => getId(item) === targetId),
      (newItem, oldItem) => {
        if (!newItem || !oldItem) return

        const instanceData = instanceRegistry.get(targetId)
        if (instanceData) {
          const createFlag = lifecycle.update(instanceData.instance, newItem, oldItem)
          if (createFlag) {
            lifecycle.destroy(instanceData.instance)
            const instance = lifecycle.create(newItem)
            if (!instance) {
              instanceData.stopHandle()
              instanceRegistry.delete(targetId)
            } else {
              instanceData.instance = instance
            }
          }
          lifecycle.changed?.()
        }
      },
      // 无需，因为所有修改经过applyChanges，底层使用 mutative 修改数据(与immer类似)
      // { deep: true },
    )
  }

  function disposeInstance(instanceData: InstanceData<P>) {
    lifecycle.destroy(instanceData.instance)
    instanceData.stopHandle()
  }

  return {
    // 提供一个获取所有实例的方法
    getInstances: () => {
      return Array.from(instanceRegistry.values()).map((instanceData) => instanceData.instance)
    },
    // 提供一个获取特定实例的方法
    getInstance: (id: T[K]): P | undefined => {
      return instanceRegistry.get(id)?.instance
    },
  }
}
