import { useEventListener } from '@vueuse/core'
import { set } from 'lodash-es'
import * as THREE from 'three'

import { ViewEvent } from '@/const'

function getProjectPos(event: MouseEvent, dom: HTMLElement, pos?: THREE.Vector2) {
  const rect = dom.getBoundingClientRect()
  const mouse = pos || new THREE.Vector2()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  return mouse
}
const INTERSECT_THRESHOLD = 0.3

export const create3DRaycast = (
  dom: HTMLElement,
  camera: THREE.Camera,
  getTargets: () => THREE.Object3D[],
) => {
  const raycaster = new THREE.Raycaster()
  set(raycaster.params, 'Line.threshold', INTERSECT_THRESHOLD)
  set(raycaster.params, 'Points.threshold', 0.5)

  return (event: MouseEvent) => {
    const pos = new THREE.Vector2()
    getProjectPos(event, dom, pos)
    raycaster.setFromCamera(pos, camera)
    const objects = getTargets()
    const intersects = raycaster.intersectObjects(objects, true)
    if (intersects.length > 0) {
      let currentTarget: THREE.Object3D | null = intersects[0].object
      while (currentTarget) {
        if (objects.includes(currentTarget)) {
          return {
            srcTarget: intersects[0].object,
            currentTarget,
          }
        }
        currentTarget = currentTarget.parent
      }
    }
    return null
  }
}

/**
 * @param eventBus 统一触发对象事件的事件总线，对象事件包括 click、dblclick、hover、unhover
 * @param intersectObject 计算鼠标事件的函数，命中时需要返回 srcTarget（触发事件的对象，可能是currentTarget或者其子节点）、currentTarget（绑定事件的对象）
 * @param eventTarget 如果传入dom则会绑定鼠标事件
 * @returns
 */
export default function useMouseRaycastObjectEvents(
  eventBus: THREE.EventDispatcher,
  intersectObject: (event: MouseEvent) => {
    srcTarget: THREE.EventDispatcher
    currentTarget: THREE.EventDispatcher
  } | null,
  eventTarget?: HTMLElement,
) {
  const mouseDownPos = new THREE.Vector2()

  let mouseDown = false
  let clickValid = false

  const onMousedown = (event: MouseEvent) => {
    mouseDown = true
    mouseDownPos.set(event.offsetX, event.offsetY)
  }

  const onMouseup = (event: MouseEvent) => {
    const distance = new THREE.Vector2(event.offsetX, event.offsetY).distanceTo(mouseDownPos)
    clickValid = mouseDown && distance < 10
    mouseDown = false
  }

  const onClick = (event: MouseEvent) => {
    if (!clickValid) return

    const match = intersectObject(event)
    if (match) {
      eventBus.dispatchEvent({
        type: ViewEvent.OBJECT_CLICK,
        ...match,
      })
      match.currentTarget.dispatchEvent({
        type: 'click',
        ...match,
      })
    }
  }

  const onDblclick = (event: MouseEvent) => {
    const match = intersectObject(event)
    if (match) {
      event.stopPropagation()

      eventBus.dispatchEvent({
        type: ViewEvent.OBJECT_DBLCLICK,
        ...match,
      })
      match.currentTarget.dispatchEvent({
        type: 'dblclick',
        ...match,
      })
    }
  }

  let noHover = true
  let lastHoverObject: THREE.EventDispatcher | null = null
  const hover = (match: ReturnType<typeof intersectObject>, event: MouseEvent) => {
    if (noHover && match === null) return
    noHover = !match
    eventBus.dispatchEvent({
      type: ViewEvent.OBJECT_HOVER,
      mouseEvent: event,
      ...match,
    })
    if (lastHoverObject && lastHoverObject !== match?.currentTarget) {
      lastHoverObject.dispatchEvent({
        type: 'unhover',
        currentTarget: lastHoverObject,
        srcTarget: lastHoverObject,
      })
    }
    if (noHover) {
      lastHoverObject = null
    } else if (match) {
      match.currentTarget.dispatchEvent({
        type: 'hover',
        ...match,
      })
      lastHoverObject = match.currentTarget
    }
  }

  const onMousemove = (event: MouseEvent) => {
    hover(intersectObject(event), event)
  }

  const onMouseleave = (event: MouseEvent) => {
    hover(null, event)
  }

  if (eventTarget) {
    useEventListener(eventTarget, 'mousedown', onMousedown)
    useEventListener(eventTarget, 'mouseup', onMouseup)
    useEventListener(eventTarget, 'dblclick', onDblclick)
    useEventListener(eventTarget, 'click', onClick)
    useEventListener(eventTarget, 'mousemove', onMousemove)
    useEventListener(eventTarget, 'mouseleave', onMouseleave)
  }

  return {
    onMousedown,
    onMouseup,
    onDblclick,
    onClick,
    onMouseleave,
    onMousemove,
  }
}
