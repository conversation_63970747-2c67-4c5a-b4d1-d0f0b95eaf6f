import { useEventListener } from '@vueuse/core'
import { set } from 'lodash-es'
import * as THREE from 'three'

import { ViewEvent } from '@/const'
import { get } from '@/utils'

function getProjectPos(event: MouseEvent, dom: HTMLElement, pos?: THREE.Vector2) {
  const rect = dom.getBoundingClientRect()
  const mouse = pos || new THREE.Vector2()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  return mouse
}
const INTERSECT_THRESHOLD = 0.3

export const create3DAdapter = (
  dom: HTMLElement,
  camera: THREE.Camera,
  getTargets: () => THREE.Object3D[],
) => {
  const raycaster = new THREE.Raycaster()
  set(raycaster.params, 'Line.threshold', INTERSECT_THRESHOLD)
  set(raycaster.params, 'Points.threshold', 0.5)

  return (event: MouseEvent) => {
    const pos = get(THREE.Vector2, 0)
    getProjectPos(event, dom, pos)
    raycaster.setFromCamera(pos, camera)
    const objects = getTargets()
    const intersects = raycaster.intersectObjects(objects, true)
    if (intersects.length > 0) {
      let currentTarget: THREE.Object3D | null = intersects[0].object
      while (currentTarget) {
        if (objects.includes(currentTarget)) {
          return {
            srcTarget: intersects[0].object,
            currentTarget,
            intersectedObjects: intersects.map((item) => item.object),
          }
        }
        currentTarget = currentTarget.parent
      }
    }
    return null
  }
}

export default function useMouseRaycastObjectEvents(
  eventDispatcher: THREE.EventDispatcher,
  intersectObject: (event: MouseEvent) => {
    srcTarget: THREE.EventDispatcher
    currentTarget: THREE.EventDispatcher
    intersectedObjects: THREE.EventDispatcher[]
  } | null,
  dom?: HTMLElement,
) {
  const mouseDownPos = new THREE.Vector2()

  let mouseDown = false
  let clickValid = false

  const onMousedown = (event: MouseEvent) => {
    mouseDown = true
    mouseDownPos.set(event.offsetX, event.offsetY)
  }

  const onMouseup = (event: MouseEvent) => {
    const tempVec2 = new THREE.Vector2()
    const distance = tempVec2.set(event.offsetX, event.offsetY).distanceTo(mouseDownPos)
    clickValid = mouseDown && distance < 10
    mouseDown = false
  }

  const onClick = (event: MouseEvent) => {
    if (!clickValid) return

    const match = intersectObject(event)
    if (match) {
      eventDispatcher.dispatchEvent({
        type: ViewEvent.OBJECT_CLICK,
        ...match,
      })
      match.currentTarget.dispatchEvent({
        type: 'click',
        ...match,
      })
    }
  }

  const onDblclick = (event: MouseEvent) => {
    const match = intersectObject(event)
    if (match) {
      event.stopPropagation()

      eventDispatcher.dispatchEvent({
        type: ViewEvent.OBJECT_DBLCLICK,
        ...match,
      })
      match.currentTarget.dispatchEvent({
        type: 'dblclick',
        ...match,
      })
    }
  }

  let noHover = true
  let lastHoverObject: THREE.EventDispatcher | null = null
  const hover = (match: ReturnType<typeof intersectObject>, event: MouseEvent) => {
    if (noHover && match === null) return
    noHover = !match
    eventDispatcher.dispatchEvent({
      type: ViewEvent.OBJECT_HOVER,
      mouseEvent: event,
      ...match,
    })
    if (noHover) {
      lastHoverObject?.dispatchEvent({
        type: 'unhover',
        currentTarget: lastHoverObject,
        srcTarget: lastHoverObject,
      })
      lastHoverObject = null
    } else if (match) {
      match.currentTarget.dispatchEvent({
        type: 'hover',
        ...match,
      })
      lastHoverObject = match.currentTarget
    }
  }

  const onMousemove = (event: MouseEvent) => {
    hover(intersectObject(event), event)
  }

  const onMouseleave = (event: MouseEvent) => {
    hover(null, event)
  }

  if (dom) {
    useEventListener(dom, 'mousedown', onMousedown)
    useEventListener(dom, 'mouseup', onMouseup)
    useEventListener(dom, 'dblclick', onDblclick)
    useEventListener(dom, 'click', onClick)
    useEventListener(dom, 'mousemove', onMousemove)
    useEventListener(dom, 'mouseleave', onMouseleave)
  }

  return {
    onMousedown,
    onMouseup,
    onDblclick,
    onClick,
    onMouseleave,
    onMousemove,
  }
}
