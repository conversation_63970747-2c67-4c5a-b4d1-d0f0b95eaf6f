import * as THREE from 'three'
import { markRaw, type MaybeRefOrGetter, ref, toValue, watch } from 'vue'

import usePointsMaterialControl from '@/stores/usePointsMaterialControl'

import loadPointsData from './loaders'
import PointsMaterial from './PointsMaterial'
import { createGeometry, getPointsInfo } from './utils'

type PointInfo = ReturnType<typeof getPointsInfo>

export default function usePointCloud(url: MaybeRefOrGetter<string>) {
  const pointsMaterialControl = usePointsMaterialControl()
  const material = new PointsMaterial()

  const points = new THREE.Points(createGeometry(), material)
  const onUpdate = () => points.dispatchEvent({ type: 'change' })
  pointsMaterialControl.attach(material, onUpdate)

  const loading = ref(false)
  const progress = ref(0)
  const pointInfo = ref({
    count: 0,
    hasIntensity: false,
    hasVelocity: false,
    hasRGB: false,
    intensityRange: new THREE.Vector2(),
    min: new THREE.Vector3(-1000, -1000, -1000),
    max: new THREE.Vector3(1000, 1000, 1000),
    ground: 0,

    vCount: 0,
    vRange: new THREE.Vector2(),
  })

  const loadPoints = async (url: string) => {
    loading.value = true
    progress.value = 0
    try {
      const data = await loadPointsData(url, (p) => {
        progress.value = p
      })

      points.geometry.dispose()
      points.geometry = createGeometry(data)
      points.geometry.computeBoundingSphere()

      const newPointInfo = getPointsInfo(points, data)
      updateMaterial(newPointInfo)
      Object.assign(pointInfo.value, newPointInfo)

      onUpdate()
    } finally {
      loading.value = false
      progress.value = 1
    }
  }
  watch(
    () => toValue(url),
    (url) => {
      if (url) {
        loadPoints(url)
      }
    },
    {
      immediate: true,
    },
  )

  function updateMaterial(pointInfo: PointInfo) {
    material.setOption({
      hasIntensity: pointInfo.hasIntensity,
      hasRGB: pointInfo.hasRGB,
      hasVelocity: pointInfo.hasVelocity,
    })
    const pointConfig = pointsMaterialControl.config
    const fixRange = (value: [number, number], range: [number, number]) => {
      value[0] = Math.max(value[0], range[0])
      value[1] = Math.min(value[1], range[1])
    }
    if (pointInfo.hasIntensity) {
      const { x: min, y: max } = pointInfo.intensityRange
      fixRange(pointConfig.intensityRange, [min, max])
    }
    const heightRange: [number, number] = [pointInfo.min.z, pointInfo.max.z]
    fixRange(pointConfig.heightRange, heightRange)
    fixRange(pointConfig.pointHeight, heightRange)
  }

  return {
    object: markRaw(points),
    loading,
    progress,
    pointInfo,
  }
}
