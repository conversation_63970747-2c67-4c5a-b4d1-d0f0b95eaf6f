import { <PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, Loader } from 'three'

type ICallBack = (args?: any) => void
Cache.enabled = false

class BINLoader extends Loader {
  littleEndian: boolean
  constructor() {
    super()

    this.littleEndian = true
  }

  load(url: string, onLoad: I<PERSON>allBack, onProgress?: ICallBack, onError?: ICallBack) {
    const loader = new FileLoader(this.manager)
    loader.setPath(this.path)
    loader.setResponseType('arraybuffer')
    loader.setRequestHeader(this.requestHeader)
    loader.setWithCredentials(this.withCredentials)
    loader.load(
      url,
      (data) => {
        try {
          onLoad(this.parse(data))
        } catch (e) {
          if (onError) {
            onError(e)
          }

          this.manager.itemError(url)
        }
      },
      onProgress,
      onError,
    )
  }

  parse(data: any) {
    const dataview = new DataView(data, 0)
    const _position: number[] = []
    const _intensity: number[] = []
    // kitti format, xyzi

    for (let row = 0; row < data.byteLength / (4 * 4); row += 1) {
      _position.push(dataview.getFloat32(row * 16 + 0, this.littleEndian))
      _position.push(dataview.getFloat32(row * 16 + 4, this.littleEndian))
      _position.push(dataview.getFloat32(row * 16 + 8, this.littleEndian))
      _intensity.push(dataview.getFloat32(row * 16 + 12, this.littleEndian))
    }

    return {
      position: _position,
      intensity: _intensity,
    }
  }
}

export default BINLoader
