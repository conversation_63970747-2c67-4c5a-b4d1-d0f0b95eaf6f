import { <PERSON><PERSON>, <PERSON>Loader, Loader } from 'three'

import PCDFile from './PCDFile'

type ICallBack = (args?: any) => void
type Type = 'F' | 'I' | 'U'
type SizeType = 1 | 2 | 4 | 8

Cache.enabled = false

export function getWithTypeFromDataView(
  dataview: DataView,
  offset: number,
  littleEndian: boolean,
  type: Type,
  size: SizeType,
) {
  switch (type) {
    case 'F':
      switch (size) {
        case 4:
          return dataview.getFloat32(offset, littleEndian)
        case 8:
          return dataview.getFloat64(offset, littleEndian)
        default:
          break
      }
      break
    case 'U':
      switch (size) {
        case 1:
          return dataview.getUint8(offset)
        case 2:
          return dataview.getUint16(offset, littleEndian)
        case 4:
          return dataview.getUint32(offset, littleEndian)
        default:
          break
      }
      break
    case 'I':
      switch (size) {
        case 1:
          return dataview.getInt8(offset)
        case 2:
          return dataview.getInt16(offset, littleEndian)
        case 4:
          return dataview.getInt32(offset, littleEndian)
        default:
          break
      }
      break
    default:
      break
  }
  throw 'PCD-Format: parse data failed'
}
export function correctNumber(n: number) {
  return isFinite(n) ? n : 0
}
export function correctXYZ(x: number, y: number, z: number) {
  return {
    x: correctNumber(x),
    y: correctNumber(y),
    z: correctNumber(z),
  }
}
class PCDLoader extends Loader {
  littleEndian: boolean
  constructor() {
    super()

    this.littleEndian = true
  }

  load(url: string, onLoad: ICallBack, onProgress?: ICallBack, onError?: ICallBack) {
    const loader = new FileLoader(this.manager)
    loader.setPath(this.path)
    loader.setResponseType('arraybuffer')
    loader.setRequestHeader(this.requestHeader)
    loader.setWithCredentials(this.withCredentials)
    loader.load(
      url,
      (data) => {
        try {
          onLoad(this.parse2(data))
        } catch (e) {
          if (onError) {
            onError(e)
          }

          this.manager.itemError(url)
        }
      },
      onProgress,
      onError,
    )
  }

  parse2(data: any) {
    const pcdData = PCDFile.parse(data).pointsDataMap
    const { x = [], y = [], z = [], intensity = [], i = [], rgb = [] } = pcdData

    const pointN = x.length
    const _position = new Float32Array(pointN * 3)
    const _color = new Uint8Array(pointN * 3)
    const targetI = intensity.length > 0 ? intensity : i.length > 0 ? i : undefined
    const hasColor = rgb.length > 0
    for (let i = 0; i < pointN; i++) {
      _position[i * 3] = x[i]
      _position[i * 3 + 1] = y[i]
      _position[i * 3 + 2] = z[i]
      if (hasColor) {
        _color[i * 3] = (rgb[i] >> 16) & 0x0000ff
        _color[i * 3 + 1] = (rgb[i] >> 8) & 0x0000ff
        _color[i * 3 + 2] = (rgb[i] >> 0) & 0x0000ff
      }
      if (targetI && targetI[i] > 0 && targetI[i] < 1) {
        targetI[i] = targetI[i] * 255
      }
    }
    return {
      position: _position,
      color: hasColor ? _color : [],
      intensity: targetI,
    }
  }
}

export default PCDLoader
