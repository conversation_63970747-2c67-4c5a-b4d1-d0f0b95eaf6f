import BINLoader from './BINLoader'
import PCDLoader from './PCDLoader'

export default async function loadPointsData(
  pointsUrl: string,
  onProgress?: (percent: number) => void,
): Promise<any> {
  const ext = pointsUrl.split('.').pop()?.split('?')[0] || ''

  const loader = ext === 'bin' ? new BINLoader() : new PCDLoader()

  return new Promise((resolve, reject) => {
    loader.load(
      pointsUrl,
      (data) => {
        resolve(data)
      },
      (e) => {
        if (onProgress) onProgress(e.loaded / e.total)
      },
      () => {
        reject()
      },
    )
  })
}
