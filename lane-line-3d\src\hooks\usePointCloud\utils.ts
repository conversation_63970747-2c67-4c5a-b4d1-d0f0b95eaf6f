import * as THREE from 'three'

function statisticPositionInfo(position: number[], precision = 2, index = 2) {
  const info: Record<string, number> = {}
  const len = position.length
  for (let i = 0; i < len; i = i + 3) {
    const v = position[i + index]
    const strValue = v.toFixed(precision)

    info[strValue] = info[strValue] || 0
    info[strValue]++
  }

  return info
}

function getPositionGround(position: number[]) {
  const info = statisticPositionInfo(position, 1)

  let maxKey = 0
  let maxValue = -Infinity
  Object.keys(info).forEach((attr) => {
    const key = +attr
    if (info[attr] > maxValue) {
      maxValue = info[attr]
      maxKey = key
    }
  })
  return maxKey
}

type PointAttr = 'position' | 'color' | string

function calculatePointInfo(data: Record<PointAttr, number[]>) {
  const position = data.position || []
  const intensity = data.intensity || []

  let intensityRange: [number, number] | undefined = undefined
  let ground = 0
  if (position.length > 0) ground = getPositionGround(position)
  if (intensity.length > 0) {
    let min = Infinity
    let max = -Infinity
    for (let i = 0; i < intensity.length; i++) {
      min = Math.min(intensity[i], min)
      max = Math.max(intensity[i], max)
      intensityRange = [min, max] as [number, number]
    }
  }
  return { ground, intensityRange }
}

export function getPointsInfo(points: THREE.Points, pointsData: any) {
  const { ground, intensityRange } = calculatePointInfo(pointsData)
  if (!points.geometry.boundingBox) {
    points.geometry.computeBoundingBox()
  }
  const boundingBox = points.geometry.boundingBox as THREE.Box3
  const position = points.geometry.getAttribute('position') as THREE.BufferAttribute
  const color = points.geometry.getAttribute('color') as THREE.BufferAttribute
  const velocity = points.geometry.getAttribute('velocity') as THREE.BufferAttribute

  const pointInfo = {
    count: position.count,
    hasIntensity: !!intensityRange,
    hasVelocity: velocity?.count > 0,
    hasRGB: color?.count > 0,
    intensityRange: new THREE.Vector2(),
    min: boundingBox.min.clone(),
    max: boundingBox.max.clone(),
    ground,
  }
  if (intensityRange) {
    pointInfo.intensityRange.set(intensityRange[0], intensityRange[1])
  } else {
    pointInfo.intensityRange.set(0, 0)
  }

  return pointInfo
}

interface IData {
  position: []
  color: []
  intensity: []
}

export function createGeometry(data: IData = { position: [], color: [], intensity: [] }) {
  const geometry = new THREE.BufferGeometry()
  const positionAttr = new THREE.Float32BufferAttribute(data.position || [], 3)
  const intensityAttr = new THREE.Float32BufferAttribute(data.intensity || [], 1)
  const colorAttr = new THREE.Uint8BufferAttribute(data.color || [], 3)
  geometry.setAttribute('position', positionAttr)
  geometry.setAttribute('intensity', intensityAttr)
  geometry.setAttribute('color', colorAttr)
  return geometry
}
