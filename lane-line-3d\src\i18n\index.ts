import Mustache from 'mustache'

export type LangType = 'zh' | 'en'

function getLocale<T extends Record<LangType, Record<string, string>>, D extends keyof T['en']>(
  name: D,
  locale: T,
  args?: Record<string, any>,
) {
  const lang = 'zh'
  const langObject = locale[lang]
  if (!langObject) return ''
  let msg = langObject[name as any] || ''
  if (args) {
    msg = Mustache.render(msg, args)
  }
  return msg
}

export function bindLocale<T extends Record<LangType, Record<string, string>>>(locale: T) {
  const bindGet = <D extends keyof T['en']>(name: D, args?: Record<string, any>) => {
    return getLocale(name, locale, args)
  }
  return bindGet
}
