/**
 * 交互状态，进入后锁定 View。
 * 通过 useInteractionManager 实现同一时间只能有一个交互状态。
 * 当前交互状态在交互完成、按下Esc、进入其他交互状态时结束。
 */
export default class BaseInteraction<P = any, R = any> {
  promise: Promise<R> | null = null
  resolve: ((data: R) => void) | null = null
  reject: ((reason?: any) => void) | null = null
  params?: P
  enabled: boolean = true

  constructor() {}

  isEnable(): boolean {
    return this.enabled
  }
  /**
   * 开始交互，由 InteractionManager 调用，
   * 可以被 stop 中断。
   */
  async execute(params?: P) {
    if (this.promise) return this.promise

    this.params = params

    this.promise = new Promise<R>((resolve, reject) => {
      this.resolve = resolve
      this.reject = reject
      this.start()
    }).finally(async () => {
      this.promise = null
      this.resolve = null
      this.reject = null
      this.end()
    })
    return this.promise
  }
  /**
   * 提前终止交互，
   * 由 InteractionManager 调用
   */
  stop = () => {
    this.reject?.()
  }

  /**
   * 开始交互时调用
   * 子类应重写此方法实现具体交互逻辑
   */
  start() {}
  /**
   * 结束交互时调用
   * 子类应重写此方法实现清理逻辑
   */
  end() {}
  /**
   * 销毁交互实例
   * 子类应重写此方法实现资源释放
   */
  destroy() {}
}
