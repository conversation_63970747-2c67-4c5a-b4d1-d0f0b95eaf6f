import { throttle } from 'lodash-es'

import { isRight } from '@/utils'

import BaseInteraction from './BaseInteraction'

interface Point2D {
  x: number
  y: number
}

interface IParams {
  minPoints?: number
  maxPoints?: number
  trackLine?: boolean
  fill?: boolean
}

interface Converter<T = Point2D> {
  clientToWorld: (clientPos: Point2D) => T
  worldToClient: (worldPos: T) => Point2D
}

export default class ConnectPointsInteraction<T = Point2D> extends BaseInteraction<IParams, T[]> {
  container: HTMLElement
  eventForwardTarget: HTMLElement
  canvas: HTMLCanvasElement
  context: CanvasRenderingContext2D
  points: T[] = []
  lastPos: Point2D = { x: 0, y: 0 }
  converter?: Converter<T>

  constructor(container: HTMLElement, eventForwardTarget: HTMLElement, converter?: Converter<T>) {
    super()

    this.container = container
    this.eventForwardTarget = eventForwardTarget
    this.converter = converter

    const canvas = document.createElement('canvas')
    canvas.className = 'ConnectPointsInteraction'
    Object.assign(canvas.style, {
      position: 'absolute',
      left: '0px',
      top: '0px',
      width: '100%',
      height: '100%',
      display: 'none',
      cursor: 'crosshair',
    })

    const context = canvas.getContext('2d') as any

    this.canvas = canvas
    this.context = context

    this.drawPointer = throttle(this.drawPointer, 20)

    this.container.appendChild(this.canvas)
    this.canvas.addEventListener('mousedown', this.onMouseDown)
    this.canvas.addEventListener('mousemove', this.onMouseMove)
    this.canvas.addEventListener('mouseleave', this.forwardEvent)
    this.canvas.addEventListener('wheel', this.forwardEvent, { passive: false })
    this.canvas.addEventListener('pointerdown', this.onPointerDown)
    this.canvas.addEventListener('contextmenu', this.onContextMenu)
  }

  override destroy = () => {
    this.container.removeChild(this.canvas)
    this.canvas.removeEventListener('mousedown', this.onMouseDown)
    this.canvas.removeEventListener('mousemove', this.onMouseMove)
    this.canvas.removeEventListener('mouseleave', this.forwardEvent)
    this.canvas.removeEventListener('wheel', this.forwardEvent)
    this.canvas.removeEventListener('pointerdown', this.onPointerDown)
    this.canvas.removeEventListener('contextmenu', this.onContextMenu)
  }

  override start = () => {
    this.points = []
    this.clear()

    this.canvas.style.display = 'block'
  }

  override end = () => {
    this.canvas.style.display = 'none'
  }

  getClientPoints = () => {
    return this.points.map((p) => this.converter?.worldToClient(p) ?? p) as Point2D[]
  }

  undoDraw = () => {
    if (this.points.length === 0) return

    this.points.pop()
    this.render()
  }

  finishCurrentDraw = () => {
    const minPoints = this.params?.minPoints
    if (this.promise && minPoints && this.points.length < minPoints) {
      this.reject?.(`至少需要${minPoints}个点`)
      return
    }
    this.resolve?.(this.points)
  }

  setStyle = () => {
    const context = this.context
    context.lineWidth = 1
    context.strokeStyle = '#fcff4b'
  }

  clear = () => {
    const context = this.context

    if (
      this.canvas.width !== this.canvas.clientWidth ||
      this.canvas.height !== this.canvas.clientHeight
    ) {
      this.canvas.width = this.canvas.clientWidth
      this.canvas.height = this.canvas.clientHeight
    }

    context.clearRect(0, 0, this.canvas.width, this.canvas.height)
  }

  render = () => {
    if (!this.promise) return
    this.clear()
    this.drawLine(this.lastPos)
    if (this.params?.fill) {
      this.drawFill(this.lastPos)
    }
  }

  drawTrackLine = (pos: Point2D) => {
    const context = this.context
    const { width, height } = this.canvas

    context.fillStyle = 'red'
    context.strokeStyle = 'red'
    context.lineWidth = 1
    context.beginPath()
    context.setLineDash([2, 2])

    context.moveTo(0, pos.y)
    context.lineTo(width, pos.y)

    context.moveTo(pos.x, 0)
    context.lineTo(pos.x, height)
    context.stroke()
  }

  drawLine = (pos: Point2D) => {
    const context = this.context

    if (this.params?.trackLine) {
      this.drawTrackLine(pos)
    }
    if (this.points.length <= 0) {
      this.drawPointer(pos)
      return
    }

    this.setStyle()
    context.setLineDash([])
    context.beginPath()

    const clientPoints = this.getClientPoints()
    clientPoints.forEach((p, index) => {
      if (index === 0) context.moveTo(p.x, p.y)
      else context.lineTo(p.x, p.y)
    })
    context.stroke()

    // 最后部分画虚线
    context.setLineDash([5, 5])
    context.lineTo(pos.x, pos.y)
    context.stroke()

    // 画点
    clientPoints.forEach((p) => {
      this.drawCirclePoint(p, 4, '#fcff4b')
      this.drawCirclePoint(p, 3, 'white')
    })
  }

  drawPointer = (pos: Point2D) => {
    this.context.fillRect(pos.x - 3, pos.y - 3, 6, 6)
  }

  drawFill = (pos: Point2D) => {
    if (this.points.length < 2) return

    const context = this.context
    context.fillStyle = 'rgb(255 255 255 / 20%)'
    context.beginPath()

    const clientPoints = this.getClientPoints()
    context.moveTo(clientPoints[0].x, clientPoints[0].y)
    for (let i = 1; i < clientPoints.length; i++) {
      const p = clientPoints[i]
      context.lineTo(p.x, p.y)
    }

    context.lineTo(pos.x, pos.y)
    context.closePath()
    context.fill()
  }

  deg2rad = (angleDeg: number) => {
    return (angleDeg * Math.PI) / 180
  }

  drawCirclePoint = (anchorPoint: Point2D, radius: number, color: string) => {
    const ctx = this.context
    const startAngleRad = this.deg2rad(0)
    const endAngleRad = this.deg2rad(360)
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(anchorPoint.x, anchorPoint.y, radius, startAngleRad, endAngleRad, false)
    ctx.fill()
    ctx.restore()
  }

  addPoint = (event: MouseEvent) => {
    const pos = { x: event.offsetX, y: event.offsetY }
    this.points.push(this.converter?.clientToWorld(pos) ?? (pos as T))
    if (this.params?.maxPoints && this.points.length >= this.params.maxPoints) {
      this.finishCurrentDraw()
    } else {
      this.lastPos = pos
      this.render()
    }
  }

  forwardEvent = (event: Event) => {
    let clonedEvent: Event

    if (event instanceof WheelEvent) {
      clonedEvent = new WheelEvent(event.type, event)
    } else if (event instanceof PointerEvent) {
      clonedEvent = new PointerEvent(event.type, event)
    } else {
      clonedEvent = new MouseEvent(event.type, event)
    }

    this.eventForwardTarget.dispatchEvent(clonedEvent)
  }

  onPointerDown = (event: PointerEvent) => {
    if (isRight(event)) {
      this.forwardEvent(event)
      this.canvas.setPointerCapture(event.pointerId)
      this.canvas.addEventListener('pointermove', this.onMouseMove)
      this.canvas.addEventListener('pointerup', this.onPointerUp)
    }
  }
  onPointerUp = (event: PointerEvent) => {
    if (isRight(event)) {
      this.forwardEvent(event)
      this.canvas.setPointerCapture(event.pointerId)
      this.canvas.removeEventListener('pointermove', this.onMouseMove)
      this.canvas.removeEventListener('pointerup', this.onPointerUp)
    }
  }

  onContextMenu = (event: MouseEvent) => {
    event.preventDefault()
  }

  onMouseMove = (event: MouseEvent) => {
    this.forwardEvent(event)
    this.lastPos = { x: event.offsetX, y: event.offsetY }
    this.render()
  }

  onMouseDown = (event: MouseEvent) => {
    if (!this.enabled) return
    if (isRight(event)) return
    this.addPoint(event)
  }
}
