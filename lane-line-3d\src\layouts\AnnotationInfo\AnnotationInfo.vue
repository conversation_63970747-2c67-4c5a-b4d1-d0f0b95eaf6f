<script setup lang="ts">
import { CloseCircleOutlined } from '@ant-design/icons-vue'
import { computed, ref } from 'vue'

import AttrsForm from '@/components/AttrsForm/AttrsForm.vue'
import ClassTypeTag from '@/components/ClassTypeTag.vue'
import useEditor from '@/editors/LaneLine3D/useEditor'
import { bindLocale } from '@/i18n'
import useInspection from '@/stores/useInspection'
import {
  getCommonClassAttrs,
  getDefaultCommonClassData,
  getDefaultPrivateClassData,
  getPrivateClassAttrs,
} from '@/utils/classType'

import * as locale from './lang'

const $$ = bindLocale(locale)
const editor = useEditor()
const { inspectedTrackObject, inspectedGeometry, isInspecting } = useInspection()
const updateClassType = (classTypeId?: string | number) => {
  if (!inspectedTrackObject.value) {
    return
  }
  editor.utils.updateTrackObjectById(inspectedTrackObject.value.id, (trackObject) => {
    trackObject.classId = classTypeId || ''
    trackObject.classData = getDefaultCommonClassData(
      editor.taskData.labels.find((item) => item.id === classTypeId),
    )
    trackObject.geometries.forEach((geometry) => {
      geometry.classData = getDefaultPrivateClassData(
        editor.taskData.labels.find((item) => item.id === classTypeId),
        geometry.type,
      )
    })
  })
}
const updateCommonClassData = (
  data: Record<string, any>,
  changes: { key: string; value: any }[],
) => {
  if (!inspectedTrackObject.value) {
    return
  }
  editor.utils.updateTrackObjectById(inspectedTrackObject.value.id, (trackObject) => {
    trackObject.classData = trackObject.classData || {}
    for (const { key, value } of changes) {
      trackObject.classData[key] = value
    }
  })
}
const updatePrivateClassData = (
  data: Record<string, any>,
  changes: { key: string; value: any }[],
) => {
  if (!inspectedGeometry.value) {
    return
  }
  editor.utils.updateGeometryById(inspectedGeometry.value.id, (geometry) => {
    geometry.classData = geometry.classData || {}
    for (const { key, value } of changes) {
      geometry.classData[key] = value
    }
  })
}

const classConfig = computed(() => {
  const classId = inspectedTrackObject.value?.classId
  if (!classId) return
  return editor.taskData.labels.find((item) => item.id === classId)
})

const commonClassAttrs = computed(() => {
  return getCommonClassAttrs(classConfig.value)
})
const privateClassAttrs = computed(() => {
  if (!inspectedGeometry.value) {
    return []
  }
  return getPrivateClassAttrs(classConfig.value, inspectedGeometry.value.type)
})

const activeKey = ref(['1', '2', '3'])
</script>

<template>
  <div
    v-show="isInspecting"
    class="annotation-info max-w-sm overflow-auto rounded-lg px-8 py-5 text-white"
  >
    <CloseCircleOutlined
      class="absolute right-2 top-6 z-10 cursor-pointer text-xl leading-none"
      @click="isInspecting = false"
    />
    <h3
      v-if="inspectedTrackObject"
      class="mb-0 text-white"
    >
      {{ inspectedTrackObject.name }}
      {{ inspectedGeometry ? ` - ${inspectedGeometry.id.slice(-4)}` : '' }}
    </h3>
    <a-collapse
      v-if="inspectedTrackObject"
      v-model:active-key="activeKey"
    >
      <a-collapse-panel key="1">
        <template #header>
          <div class="item-header">
            <span class="mr-2">
              {{ $$('class-title') }}
            </span>
            <Transition name="fade">
              <ClassTypeTag
                v-if="classConfig && !activeKey.includes('1')"
                :data="classConfig!"
              />
            </Transition>
          </div>
        </template>
        <div class="flex flex-wrap gap-2">
          <ClassTypeTag
            v-for="item in editor.taskData.labels"
            :key="item.name"
            :active="item.id === inspectedTrackObject.classId"
            class="min-w-12 text-center"
            :data="item"
            @click="updateClassType(item.id)"
          />
        </div>
      </a-collapse-panel>
      <a-collapse-panel
        v-show="commonClassAttrs.length"
        key="2"
      >
        <template #header>
          <div class="item-header">
            <span>
              {{ $$('attributes-title') }}
            </span>
          </div>
        </template>
        <AttrsForm
          :attrs="commonClassAttrs"
          :data="inspectedTrackObject.classData"
          @change="updateCommonClassData"
        />
      </a-collapse-panel>
      <a-collapse-panel
        v-if="inspectedGeometry"
        v-show="privateClassAttrs.length"
        key="3"
      >
        <template #header>
          <div class="item-header">
            <span>
              {{ $$('private-attributes-title') }}
            </span>
          </div>
        </template>
        <AttrsForm
          :attrs="privateClassAttrs"
          :data="inspectedGeometry.classData"
          @change="updatePrivateClassData"
        />
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style lang="scss">
.annotation-info {
  background: #1e1f23;

  .item-header {
    color: #d5d5d5;

    @apply flex h-10 items-center;
  }

  .ant-collapse-header {
    padding: 0 !important;
  }

  .ant-collapse {
    background-color: #1e1f23;
    border: none;
  }

  .ant-collapse-content-box {
    background: #1e1f23 !important;
  }

  .ant-collapse-content {
    background-color: #1e1f23;
    border: none;
  }

  .ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
    left: -15px;
  }
}
</style>
