<script lang="ts" setup>
import { ref } from 'vue'

import type { IFrame } from '@/editors/LaneLine3D/useEditor'

import TickLine from './TickLine.vue'
import TrackLine from './TrackLine.vue'

const canOperate = () => true
const visible = ref(true)
defineProps<{
  frames: IFrame[]
}>()
const index = defineModel<number>({ default: 0 })
</script>

<template>
  <div class="bottom-operation-container text-white">
    <div
      v-show="visible"
      class="i-head"
    >
      <ActionBar />
    </div>
    <div
      v-show="visible"
      class="i-body"
    >
      <div class="i-body-left">
        <div class="i-line-height i-label tick-line-title">时间轴</div>
      </div>
      <div class="i-body-right">
        <TickLine
          v-model="index"
          :frames="frames"
          :span-width="18"
        />
        <!-- 当前追终对象 -->
        <TrackLine
          v-model="index"
          :frames="frames"
          :span-width="18"
          style="height: 32px"
        />
      </div>
      <div
        v-show="!canOperate()"
        class="over-not-allowed"
      />
    </div>
  </div>
</template>

<style lang="less">
.bottom-operation-container {
  position: relative;
  display: flex;
  flex-direction: column;
  padding-left: 2px;
  margin-top: 4px;
  text-align: left;
  background-color: #1e1f22;

  .toggle-handle {
    position: absolute;
    top: -18px;
    left: 50%;
    padding: 0 15px;
    line-height: 18px;
    color: #aaa;
    text-align: center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    transform: translateX(-50%);

    &:hover {
      background: #2b2a2e;
    }
  }

  .i-line-height {
    height: 32px;
    line-height: 32px;
    white-space: nowrap;

    .comment {
      display: block;
      margin-top: 2px;
      font-size: 12px;
      line-height: 1;
      color: #2e8cf0;
      text-align: center;
    }

    .miss-icon {
      display: block;
      padding-left: 1px;
      font-size: 12px;
    }

    .invalid-icon {
      display: block;
      margin-top: 2px;
      font-size: 12px;
      line-height: 1;
      color: #ff5656;
      text-align: center;
    }

    .i-title {
      padding-left: 4px;
      font-size: 12px;
    }

    .new-badge {
      float: right;
      display: block;
      padding-right: 4px;
      font-size: 12px;
      color: red;
    }
  }

  .i-label {
    padding-left: 8px;
    overflow: hidden;
  }

  .i-head {
    // padding-left: 200px;
  }

  .i-body {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: row;

    .i-body-left {
      width: 200px;

      .tick-line-title {
        height: 36px;
        line-height: 36px;
        background-color: #23262e;
        box-shadow: inset 0 -1px 0 #43454b;
      }
    }

    .i-body-right {
      &::-webkit-scrollbar-thumb {
        background: #818181;
        border-radius: 10px;
      }

      &::-webkit-scrollbar {
        // display: none;
        position: absolute;
        height: 6px;
      }

      position: relative;
      flex: 1;
      width: 0;
      overflow: auto hidden;
      text-align: left;
      // }
    }
  }
}
</style>
