<script lang="ts" setup>
import { useParentElement, useResizeObserver } from '@vueuse/core'
import { computed, reactive, watch, watchEffect } from 'vue'

import type { IFrame } from '@/editors/LaneLine3D/useEditor'

// interface & type & constant -------------------------------------
interface <PERSON>ame<PERSON> {
  frames: Array<IFrame>
  interval?: number
  spanWidth?: number
}

// emit & props -------------------------------------
const props = withDefaults(defineProps<FrameProps>(), {
  spanWidth: 18,
  interval: 5,
})

const curFrameIndex = defineModel<number>({ default: 0 })

const parentElementRef = useParentElement()

const iState = reactive({
  containerWidth: 0,
  dragDomLeft: 0,
  isDrag: false,
})

// data && computed && watch
const lineCount = computed(() => {
  let count = 0
  const width = props.spanWidth
  let temp = 0
  const maxWidth = Math.max(width * props.frames.length, iState.containerWidth)
  while (temp + width <= maxWidth) {
    count++
    temp += width
  }
  return count
})

// 将指示器移动滚动到可视范围内
watchEffect(function scrollIndicatorIntoView() {
  if (!parentElementRef.value) return
  const container = parentElementRef.value as HTMLElement
  const width = props.spanWidth
  const needWidth = curFrameIndex.value * width
  const scrollX = container.scrollLeft
  const total = container.offsetWidth
  const offset = width
  if (scrollX + total < needWidth + offset) {
    container.scrollLeft = needWidth - total + offset
  } else if (scrollX > needWidth - width - offset) {
    container.scrollLeft = needWidth - width - offset
  }
})

watch(
  [() => props.spanWidth, curFrameIndex],
  ([spanWidth, index]) => {
    iState.dragDomLeft = index * spanWidth + spanWidth / 2 - 2
  },
  { immediate: true },
)

useResizeObserver(parentElementRef, () => {
  if (!parentElementRef.value) return
  iState.containerWidth = (parentElementRef.value as HTMLDivElement).offsetWidth
})

// methods
const needMarkTick = (index: number) => {
  return (index + 1) % props.interval === 0 || index === 0
}

function onClickTick(event: MouseEvent) {
  onChangeFrameIndex(Math.ceil(event.offsetX / props.spanWidth))
}

function onChangeFrameIndex(index: number) {
  if (index > props.frames.length) {
    index = props.frames.length
    return false
  }
  curFrameIndex.value = index - 1
  return true
}

function createDragListener() {
  let divX: number
  let maxX: number
  let tempDragDomLeft: number

  function preMouseEvent(e: MouseEvent) {
    e.stopPropagation?.()
    e.preventDefault?.()
    e.cancelBubble = true
  }

  const onMouseMove = (e: MouseEvent) => {
    const x = e.clientX - divX
    preMouseEvent(e)
    divX = e.clientX
    const tempX = iState.dragDomLeft + x
    if (tempX <= maxX && tempX > 0) {
      iState.dragDomLeft += x
    }
  }
  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    if (iState.isDrag) {
      iState.isDrag = false
      const width = props.spanWidth
      const index = Math.ceil(iState.dragDomLeft / width)
      if (index === curFrameIndex.value) {
        iState.dragDomLeft = tempDragDomLeft
      } else {
        onChangeFrameIndex(index)
      }
    }
  }
  return (e: MouseEvent) => {
    e = e || window.event
    preMouseEvent(e)
    if (e.button === 2) return
    tempDragDomLeft = iState.dragDomLeft
    iState.isDrag = true
    divX = e.clientX
    const width = props.spanWidth
    maxX = width * props.frames.length
    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
  }
}
const handleIndicatorDrag = createDragListener()
</script>

<template>
  <div
    style="height: 36px; white-space: nowrap; background-color: #23262e"
    @click.prevent="(e) => onClickTick(e)"
  >
    <div class="i-scale-head-container">
      <div
        v-for="index in lineCount"
        :key="index"
        class="ruler-container"
        :class="{ loaded: props.frames[index - 1]?.loaded }"
        :style="{ width: spanWidth + 'px' }"
      >
        <div
          v-if="needMarkTick(index - 1)"
          class="ruler-text"
        >
          {{ index }}
        </div>
        <span class="ruler-scale" />
      </div>
    </div>
  </div>
  <div
    class="i-scale-indicator"
    :style="{ left: iState.dragDomLeft + 'px' }"
    @mousedown="handleIndicatorDrag"
  >
    <span class="i-scale-indicator-bar" />
  </div>
</template>

<style lang="less">
.i-scale-indicator {
  position: absolute;
  top: 6px;
  bottom: 0;
  z-index: 7;
  width: 4px;
  pointer-events: none;

  &::after {
    position: absolute;
    top: 6px;
    left: 50%;
    width: 1px;
    height: 100%;
    content: '';
    background-color: #fff;
    transform: translateX(-50%);
  }

  .i-scale-indicator-bar {
    position: absolute;
    top: 1px;
    left: 50%;
    z-index: 3;
    display: block;
    width: 16px;
    height: 18px;
    pointer-events: auto;
    cursor: grab;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNS45OTciIHZpZXdCb3g9IjAgMCAxMiAxNS45OTciPg0KICA8cGF0aCBpZD0i6Lev5b6EXzIzNDYyIiBkYXRhLW5hbWU9Iui3r+W+hCAyMzQ2MiIgZD0iTTM4My45NjYsNDE5LjI1OGw1LjcwNS0zLjY3NFY0MDMuNjI5YzAtLjE2Mi0uMTgxLS4yOTMtLjQtLjI5MWgtMTEuMmMtLjIxNywwLS40LjEyOS0uNC4yOTFsMCwxMS45NTUsNS43LDMuNjcyQS42LjYsMCwwLDAsMzgzLjk2Niw0MTkuMjU4WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTM3Ny42NzIgLTQwMy4zMzgpIiBmaWxsPSIjZmZmZmZmIi8+DQo8L3N2Zz4=');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    transform: translateX(-50%);
  }

  .i-tool-scale-body {
    position: absolute;
    top: 6px;
    bottom: 0;
    left: 50%;
    display: block;
    width: 1px;
    border-left: 1px solid #1296db;
    transform: translateX(-50%);
  }
}

.i-scale-head-container {
  display: inline-block;
  height: 100%;
  overflow: hidden;
  cursor: pointer;

  .ruler-container {
    position: relative;
    display: inline-block;
    height: 100%;
    pointer-events: none;
    user-select: none;
    background-color: #23262e;

    &.loaded {
      background-color: #2b3452;
    }

    .ruler-text {
      position: absolute;
      bottom: 0;
      z-index: 4;
      width: 100%;
      font-family: sans-serif;
      font-size: 12px;
      line-height: 36px;
      color: #aaa;
      text-align: center;
      pointer-events: none;
    }

    .ruler-scale {
      position: absolute;
      bottom: 0;
      left: 50%;
      z-index: 1;
      width: 1px;
      height: 6px;
      border-left: 1px solid #666;
      transform: translateX(-0.5px);
    }

    .ruler-text + .ruler-scale {
      height: 10px;
      border-color: #cecfd5;
      border-width: 1px;
    }
  }
}
// }
</style>
