<script lang="ts" setup>
import type { IFrame } from '@/editors/LaneLine3D/useEditor'

withDefaults(
  defineProps<{
    frames: Array<IFrame>
    spanWidth?: number
  }>(),
  {
    spanWidth: 18,
  },
)

const curFrameIndex = defineModel<number>({ default: 0 })
</script>

<template>
  <div class="i-line-height">
    <div style="display: inline-block; height: 100%">
      <div
        v-for="(item, index) in frames"
        :key="index"
        class="i-tool-frame"
        :style="{ width: spanWidth + 'px' }"
      >
        <span
          class="i-tool-span"
          @click="curFrameIndex = index"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less">
.i-line-height {
  .i-tool-frame {
    position: relative;
    display: inline-block;
    height: 100%;
    border-right: 1px solid #1e1f22;
    transform: translateZ(0);
  }

  .i-tool-span {
    position: absolute;
    inset: 5px 0 1px;
    overflow: hidden;
    font-size: 14px;
    color: rgb(255 169 0);
    text-align: center;
    background-color: #303036;
  }
}
</style>
