<script lang="ts" setup>
import { ref } from 'vue'

import AnnotationResults from './components/AnnotationResults.vue'
import SceneForm from './components/SceneForm.vue'

const activeKey = ref('1')
</script>

<template>
  <div class="operation-panel">
    <a-tabs
      v-model:active-key="activeKey"
      class="h-full"
      size="small"
      tab-position="right"
      type="card"
    >
      <a-tab-pane key="1">
        <template #tab>
          <span
            class="iconfont icon-Results !text-2xl"
            title="标注结果"
          />
        </template>
        <AnnotationResults />
      </a-tab-pane>
      <a-tab-pane key="2">
        <template #tab>
          <span
            class="iconfont icon-Classfication !text-2xl"
            title="任务属性"
          />
        </template>
        <SceneForm />
      </a-tab-pane>
      <!-- <template #renderTabBar>
        <div class="order-2">123</div>
      </template> -->
    </a-tabs>
  </div>
</template>

<style lang="scss" scoped>
.operation-panel {
  color: #dee5eb;
  border-left: 1px solid #545454;

  :deep(.ant-tabs-right > .ant-tabs-nav) {
    min-width: auto;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
    overflow: auto;
  }

  :deep(.ant-tabs-card.ant-tabs-small > .ant-tabs-nav .ant-tabs-tab) {
    padding: 8px;
  }

  :deep(.ant-tabs-right > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane) {
    padding: 0;
  }
}
</style>
