<script lang="ts" setup>
import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'

import { type IGeometry, type ITrackObject, useEditor } from '@/editors/LaneLine3D/useEditor'
import useInspection from '@/stores/useInspection'
import useSelection from '@/stores/useSelection'

import GeometryIcon from './GeometryIcon.vue'

defineProps<{
  trackObject: ITrackObject
}>()

const editor = useEditor()
const { selectTrackObjectById, selectGeometryById } = useSelection()
const { isInspecting } = useInspection()

const inspectTrackObject = (trackObject: ITrackObject) => {
  selectTrackObjectById(trackObject.id, false)
  isInspecting.value = true
}

const handleClickTrackObject = (trackObject: ITrackObject) => {
  selectTrackObjectById(trackObject.id)
}
const handleClickGeometry = (geometry: IGeometry) => {
  selectGeometryById(geometry.id)
}
</script>

<template>
  <div
    class="trackObject-item cursor-pointer px-2.5 py-1.5 [&.selected]:bg-gray-600"
    :class="{
      selected: trackObject.geometries.some((item) => item.selected),
    }"
    @click="handleClickTrackObject(trackObject)"
  >
    <div class="mb-1 flex items-center justify-between">
      <div class="flex h-6 items-center gap-2">
        <span>{{ trackObject.name }}</span>
      </div>
      <div class="flex gap-2">
        <EditOutlined
          class="hover:text-blue-500"
          @click.stop="inspectTrackObject(trackObject)"
        />
        <DeleteOutlined
          class="hover:text-blue-500"
          @click.stop="editor.utils.deleteTrackObjectById(trackObject.id)"
        />
      </div>
    </div>
    <div class="flex flex-wrap gap-1">
      <div
        v-for="geometry in trackObject.geometries"
        :key="geometry.id"
        class="cursor-pointer rounded border border-gray-400 px-1 hover:border-red-500 [&.selected]:border-red-500"
        :class="{
          selected: geometry.selected,
        }"
        @click.stop="handleClickGeometry(geometry)"
      >
        <GeometryIcon :type="geometry.type" />
        {{ ' ' }}
        <span>{{ geometry.id.slice(-4) }}</span>
        |
        <DeleteOutlined
          class="hover:text-blue-500"
          @click.stop="editor.utils.deleteGeometryById(geometry.id)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
