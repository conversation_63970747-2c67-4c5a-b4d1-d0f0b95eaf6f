<script lang="ts" setup>
import { WarningOutlined } from '@ant-design/icons-vue'
import { computed, ref, watch } from 'vue'

import { useEditor } from '@/editors/LaneLine3D/useEditor'

import AnnotationItem from './AnnotationItem.vue'

const editor = useEditor()

const activeKey = ref<string[]>([])

const groups = computed(() => {
  const labels = editor.taskData.labels
  const trackObjects = editor.trackObjects
    .filter((item) => item.geometries.some((item) => item.frameId == editor.activeFrame.id))
    .map((item) => {
      return {
        ...item,
        geometries: item.geometries.filter((item) => item.frameId == editor.activeFrame.id),
      }
    })
  const results = labels.map((label) => {
    return {
      ...label,
      id: `${label.id}`,
      trackObjects: trackObjects.filter((item) => item.classId === label.id),
    }
  })
  const others = trackObjects.filter((item) => !labels.find((label) => label.id === item.classId))
  if (others.length === 0 && results.length > 0) {
    return results
  }
  return [
    {
      id: 'others',
      label: '无标签',
      color: '#ffa900',
      trackObjects: others,
    },
    ...results,
  ]
})

watch(
  () => groups.value.map((item) => item.id),
  (newIds, oldIds = []) => {
    const removed = oldIds.filter((id) => !newIds.includes(id))
    const added = newIds.filter((id) => !oldIds.includes(id))
    activeKey.value = activeKey.value.filter((id) => !removed.includes(id)).concat(added)
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <a-collapse
    v-model:active-key="activeKey"
    :bordered="false"
  >
    <a-collapse-panel
      v-for="group in groups"
      :key="group.id"
    >
      <template #header>
        <WarningOutlined
          v-if="group.id === 'others'"
          class="mr-2"
          :style="{ color: group.color, lineHeight: 'inherit' }"
        />
        <i
          v-else
          class="iconfont icon-lifangti mr-2"
          :style="{
            color: group.color,
            fontSize: '14px',
          }"
        />
        {{ group.label }}
        ({{ group.trackObjects.length }})
      </template>
      <AnnotationItem
        v-for="trackObject in group.trackObjects"
        :key="trackObject.id"
        :track-object="trackObject"
      />
    </a-collapse-panel>
  </a-collapse>
</template>

<style lang="scss" scoped></style>
