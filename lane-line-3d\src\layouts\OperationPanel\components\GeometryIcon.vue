<script lang="ts" setup>
import { GeometryTypes } from '@/const'

defineProps<{
  type: GeometryTypes
}>()

const GeometryTypeIcons: Record<GeometryTypes, string> = {
  [GeometryTypes.Point3D]: 'icon-point',
  [GeometryTypes.Polyline3D]: 'icon-polyline-v1',
  [GeometryTypes.Polygon3D]: 'icon-polygon-v1',
  [GeometryTypes.MultiRect2D]: 'icon-rect',
  [GeometryTypes.Box3D]: 'icon-biaozhunkuang',
}
</script>

<template>
  <i
    class="iconfont"
    :class="GeometryTypeIcons[type]"
  />
</template>

<style lang="scss" scoped>
.iconfont {
  font-size: inherit;
}
</style>
