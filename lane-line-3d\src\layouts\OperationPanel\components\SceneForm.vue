<script lang="ts" setup>
import AttrsForm from '@/components/AttrsForm/AttrsForm.vue'
import { useEditor } from '@/editors/LaneLine3D/useEditor'

const editor = useEditor()
const handleChange = (data: Record<string, any>) => {
  editor.framesData[editor.activeFrameIndex].classificationData = data
}
</script>

<template>
  <div class="scene-form min-h-full bg-[#2a2a2c]">
    <div
      v-if="editor.taskData.classifications.length === 0"
      class="flex items-center justify-center py-4"
    >
      暂无
    </div>
    <div
      v-for="item in editor.taskData.classifications"
      :key="item.id"
      class="px-2 py-1"
    >
      <div>{{ item.name }}</div>
      <AttrsForm
        :attrs="item.attrs"
        :data="editor.framesData[editor.activeFrameIndex]?.classificationData"
        @change="handleChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scene-form {
  color: #dee5eb;
  border-left: 1px solid #545454;
}
</style>
