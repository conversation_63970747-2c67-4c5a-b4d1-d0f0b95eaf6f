<template>
  <div class="pc-flow">
    <div class="item-wrap">
      <span
        :class="blocking ? 'close disable' : 'close'"
        @click="blocking ? null : onClose()"
      >
        <CloseOutlined />
        <span style="margin-left: 4px">{{ $$('btn-close') }}</span>
      </span>
      <div class="task-header-info">
        <span
          class="task-header-name"
          :title="editor.activeFrame.name"
        >
          {{ editor.activeFrame.name }}
        </span>
        <i class="iconfont icon-a-Jobinformation ml-2.5" />
      </div>
    </div>
    <div class="item-wrap">
      <!-- Save -->
      <a-button
        class="basic-btn"
        :disabled="blocking"
        :loading="editor.status.isSaving"
        size="large"
        @click="editor.methods.save()"
      >
        <template #icon><SaveOutlined /></template>
        <div class="title">{{ $$('btn-save') }}</div>
      </a-button>
      <!-- shortcut -->
      <a-button
        class="basic-btn"
        :disabled="blocking"
        size="large"
        @click="modalHelpVisible = true"
      >
        <template #icon>
          <i
            class="iconfont icon-help"
            style="font-size: 16px"
          />
        </template>
        <div class="title">{{ $$('btn-shortcut') }}</div>
      </a-button>
      <a-modal
        v-model:visible="modalHelpVisible"
        :footer="null"
        title="快捷键"
        width="1000px"
      >
        <ModalHelp />
      </a-modal>
      <!-- full screen -->
      <a-button
        class="basic-btn"
        size="large"
        @click="toggleFullscreen"
      >
        <template #icon>
          <i
            class="iconfont"
            :class="[isFullscreen ? 'icon-tuichuquanping' : 'icon-a-Fullscreen']"
            style="font-size: 16px"
          />
        </template>
        <div class="title">{{ isFullscreen ? $$('btn-full-exit') : $$('btn-full') }}</div>
      </a-button>
      <a-divider
        style="height: 32px; background-color: #57575c"
        type="vertical"
      />
      <a-button
        class="basic submit"
        :disabled="blocking"
        :loading="editor.status.isSubmitting"
        @click="onSubmit"
      >
        <template #icon><SaveOutlined /></template>
        {{ $$('btn-submit') }}
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CloseOutlined, SaveOutlined } from '@ant-design/icons-vue'
import { useFullscreen } from '@vueuse/core'
import { message, Modal } from 'ant-design-vue'
import { computed, ref } from 'vue'

import { submitData } from '@/api/flow'
import ModalHelp from '@/components/ModalHelp/ModalHelp.vue'
import { useEditor } from '@/editors/LaneLine3D/useEditor'
import { bindLocale } from '@/i18n'
import useInteractionManager from '@/stores/useInteractionManager'
import useSaveDataChangeTracker from '@/stores/useSaveDataChangeTracker'

import * as locale from './lang'

const editor = useEditor()
const modalHelpVisible = ref(false)
const $$ = bindLocale(locale)

const { isFullscreen, toggle: toggleFullscreen } = useFullscreen()
const saveDataChangeTracker = useSaveDataChangeTracker()
const interactionManager = useInteractionManager()

async function onClose() {
  if (saveDataChangeTracker.getChangedFrames().dataInfos.length > 0) {
    Modal.confirm({
      title: '保存修改',
      content: '是否保存修改？',
      okText: '保存并关闭',
      cancelText: '丢弃并关闭',
      closable: false,
      keyboard: false,
      async onOk() {
        try {
          await editor.methods.save()
          editor.status.isPendingClose = true
          setTimeout(() => {
            closeTab()
          }, 2000)
        } catch {}
      },
      onCancel() {
        closeTab()
      },
    })
  } else {
    closeTab()
  }
}

function closeTab() {
  const win = window.open('about:blank', '_self')
  if (win) {
    win.close()
  }
}

const blocking = computed(() => {
  return (
    editor.status.isLoading ||
    editor.status.isSaving ||
    editor.status.isSubmitting ||
    editor.status.isPendingClose ||
    editor.pointCloud.loading ||
    !!interactionManager.currentInteraction.value
  )
})

async function onSubmit() {
  editor.status.isSubmitting = true
  try {
    if (saveDataChangeTracker.getChangedFrames().dataInfos.length > 0) {
      await editor.methods.save({ quiet: true })
    }
    await submitData(editor.taskData.taskId)
    message.success('提交成功')
    editor.status.isPendingClose = true
    setTimeout(() => {
      closeTab()
    }, 2000)
  } finally {
    editor.status.isSubmitting = false
  }
}
</script>

<style lang="scss">
.pc-flow {
  display: flex;
  justify-content: space-between;
  height: 54px;
  color: #dee5eb;
  background: #1e1f23;

  .task-header-info {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 2px 15px;
    margin-left: 12px;
    background: rgb(58 58 62 / 39%);
    border: 1px solid rgb(58 58 62 / 39%);
    border-radius: 16px;

    .task-header-name {
      padding-right: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      line-height: 18px;
      color: #bec1ca;
      white-space: nowrap;
      border-right: 1px solid #57575c;
    }
  }

  .item-wrap {
    display: flex;
    align-items: center;

    .basic-btn {
      padding: 0 8px;
      border: none;

      .anticon {
        font-size: 16px;
      }

      .title {
        margin-top: -4px;
        font-size: 14px;
      }

      &.ant-btn:hover,
      &.ant-btn:focus {
        color: white;
        border-color: #434343;
      }
    }

    .header-info {
      display: flex;
      padding: 5px 15px;
      margin-left: 20px;
      color: #bec1ca;
      background-color: #3a393e;
      border-radius: 16px;
    }

    .icon {
      margin: 0 4px;
      font-size: 20px;
      cursor: pointer;
    }

    .icon.disable {
      color: #5a5a5a;
      cursor: not-allowed;
    }

    .text {
      margin-left: 4px;
      font-size: 18px;
    }
  }

  .close {
    margin-left: 10px;
    font-size: 20px;
    cursor: pointer;

    &.disable {
      color: #5a5a5a;
      cursor: not-allowed;
    }
  }

  .data-index {
    .ant-input-number-handler-wrap {
      display: none;
    }

    .ant-input-number-sm input {
      text-align: center;
    }
  }

  .basic {
    margin-right: 10px;
    border-radius: 30px;

    &.mark-invalid {
      background-color: #fcb17a;
    }

    &.mark-valid {
      background-color: #49aa19;
    }

    &.skip {
      background-color: #98b0d2;
    }

    &.skipped,
    &.modify {
      background-color: #ff6906;
    }

    &.submit {
      padding-left: 15px !important;
      background-color: #60a9fe99;

      .anticon {
        width: 32px;
        height: 30px;
        padding-top: 5px;
        margin-top: -4px;
        margin-left: -16px;
        background: #60a9fe;
        border-radius: 16px;
      }
    }

    /* stylelint-disable-next-line no-descending-specificity */
    .anticon {
      font-size: 20px;
      vertical-align: middle;
    }

    &.ant-btn:hover,
    &.ant-btn:focus {
      color: white;
      border-color: #434343;
    }
  }

  .dataset-name {
    display: inline-block;
    max-width: 100px;
  }
}
</style>
