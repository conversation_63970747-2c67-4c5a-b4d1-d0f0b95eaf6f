<script lang="ts" setup>
import { useEditor } from '@/editors/LaneLine3D/useEditor'

import GlobalSettingsItem from './items/GlobalSettings/GlobalSettings.vue'
import PointCloudInfoItem from './items/PointCloudInfo/PointCloudInfo.vue'
import RunModelItem from './items/RunModel/RunModel.vue'

const editor = useEditor()
</script>

<template>
  <div class="flex w-12 flex-col justify-between gap-2 overflow-y-auto">
    <div class="flex w-full flex-1 flex-col items-center gap-1">
      <template
        v-for="(item, key) in editor.toolbar"
        :key="key"
      >
        <component :is="item" />
      </template>
      <RunModelItem />
    </div>
    <div class="flex w-full flex-col-reverse items-center gap-1 py-1">
      <GlobalSettingsItem />
      <PointCloudInfoItem />
    </div>
  </div>
</template>

<style lang="scss">
.tool-info-tooltip {
  width: 250px;
  max-width: 250px;
  min-height: 100px;
  padding: 4px;
  font-size: 12px;
  color: #bec1ca;
  background: #333;
  border-radius: 4px;

  .ant-slider-handle {
    background-color: #fff;
    border: solid 2px #2e8cf0;
  }

  .ant-slider-track {
    background-color: #2e8cf0;
  }

  .ant-tooltip-inner {
    color: #bec1ca;
    box-shadow: none;
  }

  .ant-checkbox-wrapper {
    margin-right: 10px !important;
    margin-bottom: 10px !important;
    margin-left: 0 !important;
    font-size: 12px;
    color: #bec1ca;
  }

  .divider {
    display: inline-block;
    width: 240px;
    vertical-align: middle;
    border-top: 1px solid #6c6c6c;
  }

  .ant-tooltip-arrow {
    display: none;
  }

  .ant-tooltip-content {
    position: relative;

    .close {
      position: absolute;
      top: 6px;
      right: 6px;
      font-size: 20px;
      cursor: pointer;
    }
  }

  .wrap {
    padding-left: 14px;
  }

  .title1 {
    font-size: 16px;
    line-height: 36px;
    color: white;
    text-align: center;
  }

  .title2 {
    font-size: 12px;
    line-height: 30px;
    color: white;
  }

  .title3 {
    font-size: 12px;
    line-height: 26px;
  }
}
</style>
