<script lang="ts" setup>
import { EllipsisOutlined } from '@ant-design/icons-vue'
defineProps<{
  name: string
  active?: boolean
  showBadge?: boolean
  extraTitle?: string
  onClick?: (e: MouseEvent) => void
}>()
</script>

<template>
  <div
    class="toolbar-item flex h-10 w-10 flex-col text-xs text-gray-300"
    :class="{ active }"
  >
    <div
      class="relative block flex-1"
      :title="name"
      @click="onClick"
    >
      <slot name="icon" />
      <span
        v-show="showBadge"
        class="absolute right-0.5 top-0.5 inline-block h-3.5 w-3.5 rounded-full bg-red-600 text-center text-sm leading-none text-white"
      >
        +
      </span>
    </div>
    <a-tooltip
      v-if="$slots.extra"
      overlay-class-name="tool-info-tooltip"
      placement="rightBottom"
      trigger="click"
    >
      <template #title>
        <slot name="extra" />
      </template>
      <div
        class="block"
        :title="extraTitle"
      >
        <EllipsisOutlined class="border-t border-gray-600" />
      </div>
    </a-tooltip>
  </div>
</template>

<style lang="scss" scoped>
.block {
  @apply flex cursor-pointer items-center justify-center rounded bg-slate-950 hover:bg-[#224b77];

  &:nth-last-child(2) {
    border-radius: 4px 4px 0 0;
  }

  &:nth-child(2) {
    border-radius: 0 0 4px 4px;
  }
}

.toolbar-item.active {
  .block:first-child {
    color: #dee5eb;
    background: #0486fe;
  }
}
</style>
