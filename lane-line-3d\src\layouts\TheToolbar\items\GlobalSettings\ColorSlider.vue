<script lang="ts" setup>
import { RetweetOutlined } from '@ant-design/icons-vue'
import * as THREE from 'three'
import { computed } from 'vue'

import ColorPicker from '@/components/ColorPicker'
import { useEditor } from '@/editors/LaneLine3D/useEditor'
import { ColorModeEnum, getThemeColor } from '@/hooks/usePointCloud/PointsMaterial'
import { bindLocale } from '@/i18n'
import usePointsMaterialControl from '@/stores/usePointsMaterialControl'

import * as locale from '../../lang'

const editor = useEditor()
const pointInfo = editor.pointCloud.pointInfo

const { config: pointsMaterialConfig } = usePointsMaterialControl()
const $$ = bindLocale(locale)

const colorCodeBg = () => {
  let color: string[] = []
  if (pointsMaterialConfig.colorMode === ColorModeEnum.VELOCITY) {
  } else {
    const colors = getThemeColor(pointsMaterialConfig.edgeColor)
    const _min = pointInfo.min.z
    const _max = pointInfo.max.z
    const [min, max] = pointsMaterialConfig.pointHeight
    const mapLinear = (value: number) => {
      const height = THREE.MathUtils.mapLinear(value, 0, colors.length - 1, min, max)
      const ratio = THREE.MathUtils.mapLinear(height, _min, _max, 0, 100)
      return ratio
    }
    color = [
      `${pointsMaterialConfig.edgeColor[0]} 0%`,
      `${pointsMaterialConfig.edgeColor[0]} ${mapLinear(0)}%`,
    ]
    colors.forEach((item, index) => {
      color.push(`${item} ${mapLinear(index)}%`)
    })
    color.push(
      `${pointsMaterialConfig.edgeColor[1]} ${mapLinear(colors.length - 1)}%`,
      `${pointsMaterialConfig.edgeColor[1]} 100%`,
    )
  }
  return `linear-gradient(90deg, ${color.join(',')})`
}

const _pointHeight = computed(() => {
  return pointsMaterialConfig.pointHeight.map((value) => {
    return THREE.MathUtils.clamp(value, round(pointInfo.min.z), round(pointInfo.max.z))
  }) as [number, number]
})
function onChangeHeight(value: any) {
  pointsMaterialConfig.pointHeight = value
}

function tipFormatter(value: any) {
  return Number(value).toFixed(1)
}

function onResetHeight() {
  pointsMaterialConfig.pointHeight = [pointInfo.ground, pointInfo.max.z]
}

function formatter(value: any) {
  const n = ('' + value).split('.')
  if (n[1] && n[1].length > 1) {
    return Number(value).toFixed(1)
  } else {
    return value
  }
}

function onResetColor() {
  const mode = pointsMaterialConfig.colorMode
  if (mode === ColorModeEnum.PURE) {
    pointsMaterialConfig.singleColor = '#87abff'
  } else if ([ColorModeEnum.HEIGHT].includes(mode)) {
    pointsMaterialConfig.edgeColor = ['#000dff', '#ff0000']
  }
}
function round(value: any) {
  return Math.round(value * 10) / 10
}
</script>

<template>
  <div
    v-if="[ColorModeEnum.HEIGHT].includes(pointsMaterialConfig.colorMode)"
    class="title3"
    style="padding: 6px 0 0 14px"
  >
    {{ $$('setting_colorheight') }}
    <a-button
      size="small"
      style="float: right; border: none"
      :title="$$('setting_pointreset')"
      @click="onResetHeight"
    >
      <template #icon>
        <RetweetOutlined />
      </template>
    </a-button>

    <a-input-number
      v-model:value="pointsMaterialConfig.pointHeight[1]"
      :formatter="formatter"
      :max="pointInfo.max.z"
      :min="pointsMaterialConfig.pointHeight[0]"
      size="small"
      :step="0.1"
      style="float: right; width: 60px"
    />
    <a-input-number
      v-model:value="pointsMaterialConfig.pointHeight[0]"
      :formatter="formatter"
      :max="pointsMaterialConfig.pointHeight[1]"
      :min="pointInfo.min.z"
      size="small"
      :step="0.1"
      style="float: right; width: 60px"
    />
  </div>
  <div
    v-if="pointsMaterialConfig.colorMode === ColorModeEnum.HEIGHT"
    class="color-item-container"
  >
    <a-tooltip
      placement="topLeft"
      trigger="click"
    >
      <template #title>
        <ColorPicker
          v-model:pure-color="pointsMaterialConfig.edgeColor[0]"
          :disable-alpha="true"
          :disable-history="true"
          :is-widget="true"
          picker-type="chrome"
          use-type="pure"
        />
      </template>
      <div
        class="color-span"
        :style="{ background: pointsMaterialConfig.edgeColor[0] }"
      />
    </a-tooltip>
    <div
      class="color-slider"
      :style="{ background: colorCodeBg() }"
    >
      <a-slider
        v-if="pointsMaterialConfig.colorMode === ColorModeEnum.HEIGHT"
        :max="round(pointInfo.max.z)"
        :min="round(pointInfo.min.z)"
        range
        :step="0.1"
        style="width: 100%; margin: 0"
        :tip-formatter="tipFormatter"
        :value="_pointHeight"
        @change="onChangeHeight"
      />
    </div>
    <a-tooltip
      placement="topLeft"
      trigger="click"
    >
      <template #title>
        <ColorPicker
          v-model:pure-color="pointsMaterialConfig.edgeColor[1]"
          :disable-alpha="true"
          :disable-history="true"
          :is-widget="true"
          picker-type="chrome"
          use-type="pure"
        />
      </template>
      <div
        class="color-span"
        :style="{ background: pointsMaterialConfig.edgeColor[1] }"
      />
    </a-tooltip>
  </div>
  <div
    v-show="pointsMaterialConfig.colorMode === ColorModeEnum.PURE"
    class="color-item-container"
  >
    <a-tooltip
      placement="topLeft"
      trigger="click"
    >
      <template #title>
        <ColorPicker
          v-model:pure-color="pointsMaterialConfig.singleColor"
          :disable-alpha="true"
          :disable-history="true"
          :is-widget="true"
          picker-type="chrome"
          use-type="pure"
        />
      </template>
      <div
        class="color-span"
        :style="{ background: pointsMaterialConfig.singleColor }"
      />
    </a-tooltip>
  </div>
  <div
    class="title3"
    style="height: 24px; margin-top: 10px; margin-bottom: 4px"
  >
    <a-button
      class="reset"
      size="small"
      type="dashed"
      @click="onResetColor"
    >
      {{ $$('setting_colorreset') }}
    </a-button>
  </div>
</template>

<style lang="less">
.color-item-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-height: 10px;
  margin-top: 8px;

  .color-span {
    width: 16px;
    height: 16px;
    border: 1px solid white;
  }

  .color-slider {
    position: relative;
    flex: 1;
    height: 10px;
    margin: 0 8px;

    .color-slider-indicator {
      position: absolute;
      top: -4px;
      bottom: -4px;
      width: 4px;
      cursor: pointer;
      background: white;
      border-radius: 2px;
    }

    .ant-slider-track {
      background-color: transparent !important;
    }

    .ant-slider-rail {
      background-color: transparent !important;
    }
  }
}
</style>
