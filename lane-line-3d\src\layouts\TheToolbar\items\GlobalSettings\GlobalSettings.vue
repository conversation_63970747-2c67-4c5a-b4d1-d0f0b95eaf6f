<script lang="ts" setup>
import ToolbarItem from '../../ToolbarItem.vue'
import GlobalSettingsPanel from './GlobalSettingsPanel.vue'
</script>

<template>
  <a-tooltip
    overlay-class-name="tool-info-tooltip"
    placement="rightBottom"
    trigger="click"
  >
    <template #title>
      <GlobalSettingsPanel />
    </template>

    <ToolbarItem name="设置">
      <template #icon><i class="iconfont icon-xianshi" /></template>
    </ToolbarItem>
  </a-tooltip>
</template>
