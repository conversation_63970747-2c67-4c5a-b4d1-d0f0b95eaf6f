<script setup lang="ts">
import { bindLocale } from '@/i18n'

import * as locale from '../../lang'
import PointCloudConfig from './PointCloudConfig.vue'

const $$ = bindLocale(locale)
</script>

<template>
  <div class="setting">
    <div class="title1 border-bottom">{{ $$('setting_display') }}</div>
    <div class="wrap">
      <PointCloudConfig />
    </div>
  </div>
</template>

<style lang="scss">
.setting {
  .reset {
    float: right;
    font-size: 12px;
    color: #bec1ca;
    border: 1px solid #bec1ca;
  }
}
</style>
