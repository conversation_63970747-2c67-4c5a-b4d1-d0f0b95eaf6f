<script setup lang="ts">
import { RetweetOutlined } from '@ant-design/icons-vue'

import { useEditor } from '@/editors/LaneLine3D/useEditor'
import { ColorModeEnum } from '@/hooks/usePointCloud/PointsMaterial'
import { bindLocale } from '@/i18n'
import usePointsMaterialControl from '@/stores/usePointsMaterialControl'

import * as locale from '../../lang'
import ColorSlider from './ColorSlider.vue'

const $$ = bindLocale(locale)
const editor = useEditor()

const pointInfo = editor.pointCloud.pointInfo
const { config: pointConfig } = usePointsMaterialControl()

// *********************************************
const formatter = (value?: number) => {
  return value?.toFixed(2)
}

function onResetSize() {
  pointConfig.pointSize = 0.1
}

function onResetIntensity() {
  pointConfig.intensityRange = [0, 255]
}
function onResetBrightness() {
  pointConfig.brightness = 1
}
</script>

<template>
  <div class="setting">
    <div class="title2">
      <span style="margin-right: 10px; vertical-align: middle">
        {{ $$('setting_pointview') }}
      </span>
    </div>
    <div class="wrap">
      <div class="title3">
        {{ $$('setting_pointsize') }}
        <a-button
          class="reset"
          size="small"
          type="dashed"
          @click="onResetSize"
        >
          {{ $$('setting_pointreset') }}
        </a-button>
      </div>
      <a-slider
        v-model:value="pointConfig.pointSize"
        :max="10"
        :min="1"
        :step="0.1"
        style="width: 200px; margin: 0; margin-top: 5px"
        :tip-formatter="formatter"
      />
      <div class="title3">
        {{ $$('setting_brightness') }}
        <a-button
          class="reset"
          size="small"
          type="dashed"
          @click="onResetBrightness"
        >
          {{ $$('setting_pointreset') }}
        </a-button>
      </div>
      <a-slider
        v-model:value="pointConfig.brightness"
        :max="2"
        :min="0.1"
        :step="0.1"
        style="width: 200px; margin: 0; margin-top: 5px"
        :tip-formatter="formatter"
      />
    </div>
    <div class="wrap">
      <div class="title3">
        {{ $$('setting_pointcolor') }}
        <br />
        <a-radio-group
          v-model:value="pointConfig.colorMode"
          size="small"
          style="margin-top: 5px; font-size: 12px"
        >
          <a-radio-button
            style="width: 80px; text-align: center"
            :value="ColorModeEnum.PURE"
          >
            {{ $$('setting_colorsingle') }}
          </a-radio-button>
          <a-radio-button
            style="width: 80px; text-align: center"
            :value="ColorModeEnum.HEIGHT"
          >
            {{ $$('setting_colorheight') }}
          </a-radio-button>
        </a-radio-group>
      </div>
    </div>
    <ColorSlider />
    <div
      v-if="pointInfo.hasIntensity"
      class="wrap"
    >
      <div class="title3">
        {{ $$('setting_colorintensity') }}
        <a-switch
          v-model:checked="pointConfig.openIntensity"
          size="small"
          style="float: right; margin-top: 5px"
        />
      </div>
      <div
        v-show="pointConfig.openIntensity"
        class="title3"
        style="padding-top: 10px"
      >
        <a-input-number
          v-model:value="pointConfig.intensityRange[0]"
          :max="255"
          :min="0"
          size="small"
          style="width: 80px"
        />
        <a-input-number
          v-model:value="pointConfig.intensityRange[1]"
          :max="255"
          :min="0"
          size="small"
          style="width: 80px"
        />
        <a-button
          size="small"
          style="border: none"
          :title="$$('setting_pointreset')"
          @click="onResetIntensity"
        >
          <template #icon>
            <RetweetOutlined />
          </template>
        </a-button>
      </div>
      <div
        v-show="pointConfig.openIntensity"
        class="title3"
        style="width: 180px; margin: 5px 0 0 5px; margin-top: 5px"
      >
        <a-slider
          v-model:value="pointConfig.intensityRange"
          :max="255"
          :min="0"
          range
          :step="0.1"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss"></style>
