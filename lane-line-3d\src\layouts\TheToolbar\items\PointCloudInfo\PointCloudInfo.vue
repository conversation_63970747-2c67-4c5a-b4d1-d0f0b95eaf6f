<script lang="ts" setup>
import ToolbarItem from '../../ToolbarItem.vue'
import PointCloudInfoPanel from './PointCloudInfoPanel.vue'
</script>

<template>
  <a-tooltip
    overlay-class-name="tool-info-tooltip"
    placement="rightBottom"
    trigger="click"
  >
    <template #title>
      <PointCloudInfoPanel />
    </template>
    <ToolbarItem name="信息">
      <template #icon><i class="iconfont icon-xinxi" /></template>
    </ToolbarItem>
  </a-tooltip>
</template>
