<script setup lang="ts">
import { useEditor } from '@/editors/LaneLine3D/useEditor'
import { bindLocale } from '@/i18n'
import { formatNumber } from '@/utils/format'

import * as locale from '../../lang'

const editor = useEditor()
const $$ = bindLocale(locale)
const pointInfo = editor.pointCloud.pointInfo
</script>
<template>
  <div class="statistic-info">
    <div class="title1 border-bottom">{{ $$('info_title') }}</div>
    <div class="wrap">
      <div class="title2">
        <span style="margin-right: 10px; vertical-align: middle">{{ $$('info_datainfo') }}</span>
      </div>
      <a-row>
        <a-col :span="2">X:</a-col>
        <a-col :span="10">Min {{ formatNumber(pointInfo.min.x) }}</a-col>
        <a-col :span="10">Max {{ formatNumber(pointInfo.max.x) }}</a-col>
      </a-row>
      <a-row>
        <a-col :span="2">Y:</a-col>
        <a-col :span="10">Min {{ formatNumber(pointInfo.min.y) }}</a-col>
        <a-col :span="10">Max {{ formatNumber(pointInfo.max.y) }}</a-col>
      </a-row>
      <a-row>
        <a-col :span="2">Z:</a-col>
        <a-col :span="10">Min {{ formatNumber(pointInfo.min.z) }}</a-col>
        <a-col :span="10">Max {{ formatNumber(pointInfo.max.z) }}</a-col>
      </a-row>
      <div class="title2">
        <span style="margin-right: 10px; vertical-align: middle">{{ $$('info_pointinfo') }}</span>
      </div>
      <a-row>
        <a-col :span="12">{{ $$('info_pointall') }}： {{ pointInfo.count }}</a-col>
        <a-col :span="12">{{ $$('info_pointvisible') }}： {{ pointInfo.vCount }}</a-col>
      </a-row>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
