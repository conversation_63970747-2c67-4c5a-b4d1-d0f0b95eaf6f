<template>
  <div
    ref="containerRef"
    class="editor-info-tooltip"
    style="width: 230px; padding: 0 4px"
  >
    <div style="padding-bottom: 4px; font-size: 14px; color: white; border-bottom: 1px solid gray">
      {{ $$('model_title') }}
    </div>
    <div class="title2">
      <span style="margin-right: 10px; vertical-align: middle">{{ $$('model_name') }}</span>
    </div>
    <div class="title2">
      <a-select
        v-model:value="modelConfig.modelName"
        :get-popup-container="() => containerRef!"
        :options="options"
        style="width: 100%; font-size: 12px"
      />
    </div>
    <div class="title2">
      <a-checkbox v-model:checked="modelConfig.predict">{{ $$('model_predict') }}</a-checkbox>
    </div>
    <div v-show="!modelConfig.predict">
      <div class="title2">{{ $$('model_predict') }}</div>
      <div
        class="title2"
        style="display: flex; flex-direction: row"
      >
        <div>
          <a-input-number
            v-model:value="modelConfig.confidence[0]"
            :max="modelConfig.confidence[1]"
            :min="0"
            size="small"
            :step="0.1"
            style="width: 60px"
          />
        </div>
        <div style="flex: 1">
          <a-slider
            v-model:value="modelConfig.confidence"
            :max="1"
            :min="0"
            range
            :step="0.1"
            style="padding: 0; margin-right: 10px"
          />
        </div>
        <div>
          <a-input-number
            v-model:value="modelConfig.confidence[1]"
            :max="1"
            :min="modelConfig.confidence[0]"
            size="small"
            :step="0.1"
            style="width: 60px"
          />
        </div>
      </div>
      <div class="title2">
        <span style="margin-right: 10px">{{ $$('model_classes') }}</span>
        <a @click.prevent.stop="() => onSelectAll()">
          {{ hasUnselectedClasses ? $$('model_select_all') : $$('model_unselect_all') }}
        </a>
      </div>
      <div class="title2">
        <a-tag
          v-for="tag in curClasses"
          :key="tag.value"
          :color="tag.selected ? '#177ddc' : ''"
          style="cursor: pointer; user-select: none"
          @click="() => onTagSwitch(tag.value)"
        >
          {{ tag.label }}
          <CloseOutlined />
        </a-tag>
      </div>
    </div>
    <div
      class="title2"
      style="padding-top: 6px; margin-top: 10px; text-align: right; border-top: 1px solid gray"
    >
      <a-button
        size="small"
        style="margin-right: 10px"
        @click="onReset"
      >
        {{ $$('model_reset') }}
      </a-button>
      <a-button
        :loading="loading"
        size="small"
        type="primary"
        @click="onModelRun"
      >
        {{ complete ? $$('model_add') : $$('model_run') }}
      </a-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { CloseOutlined } from '@ant-design/icons-vue'
import { computed, ref } from 'vue'

import { bindLocale } from '@/i18n'

import * as locale from '../../lang'
import useAIModel from './useAIModel'

const { modelConfig, results, models, runModel } = useAIModel()
const $$ = bindLocale(locale)
// ***************Props and Emits***************
const containerRef = ref<HTMLDivElement>()
// ***************Props and Emits***************
const loading = computed(() => results.isRunning)
const complete = computed(() => {
  return !!results.objects
})

const onTagSwitch = function (value: string) {
  const classes = modelConfig.classes[modelConfig.modelName]
  if (classes) {
    const tag = classes.find((item) => item.value === value)
    if (tag) {
      tag.selected = !tag.selected
    }
  }
}
const curClasses = computed(() => {
  return modelConfig.classes[modelConfig.modelName] || []
})
const hasUnselectedClasses = computed(() => {
  return curClasses.value.some((item) => !item.selected)
})
function onSelectAll(selected?: boolean) {
  const selectionState = selected ?? hasUnselectedClasses.value
  modelConfig.classes[modelConfig.modelName]?.forEach((item) => {
    item.selected = selectionState
  })
}
const options = computed(() => {
  return models.value.map((e) => {
    return { value: e.name, label: e.name }
  })
})
function onReset() {
  modelConfig.confidence = [0.5, 1]
  onSelectAll(true)
}
function onModelRun() {
  runModel()
}
</script>
