<script lang="ts" setup>
import ToolbarItem from '../../ToolbarItem.vue'
import ModelConfigPanel from './ModelConfigPanel.vue'
import useAIModel from './useAIModel'

const { results, runModel, addResults, isReady } = useAIModel()
const handleClick = () => {
  if (!isReady.value || results.isRunning) return
  if (results.objects) {
    addResults()
  } else {
    runModel()
  }
}
</script>

<template>
  <ToolbarItem
    extra-title="配置"
    name="跑模型"
    :show-badge="!!results.objects"
    @click="handleClick"
  >
    <template #icon>
      <i
        v-if="results.isRunning"
        class="iconfont icon-loading loading"
      />
      <i
        v-else
        class="iconfont icon-Vector"
      />
    </template>
    <template #extra><ModelConfigPanel /></template>
  </ToolbarItem>
</template>

<style lang="scss" scoped></style>
