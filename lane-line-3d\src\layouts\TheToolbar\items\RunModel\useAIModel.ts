import { createGlobalState, useAsyncState } from '@vueuse/core'
import { message } from 'ant-design-vue'
import { reactive, watch } from 'vue'

import { getModelList, getModelResult, startRunModel } from '@/api/model'
import type { Object2DLine } from '@/api/typings/model'
import { GeometryTypes, SourceType } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'

export interface IModelClass {
  label: string
  value: string
  selected: boolean
}

export interface IModelConfig {
  modelName: string
  predict: boolean
  confidence: [number, number]
  classes: { [key: string]: IModelClass[] }
}

const useAIModel = createGlobalState(() => {
  const editor = useEditor()
  const modelConfig = reactive<IModelConfig>({
    modelName: '',
    predict: true,
    confidence: [0.5, 1],
    classes: {},
  })

  const { state: models, isReady } = useAsyncState(getModelList(), [])

  watch(
    () => models.value,
    () => {
      const model = models.value[0]
      if (!modelConfig.modelName && model) {
        modelConfig.modelName = model.name
      }
    },
    { immediate: true },
  )

  watch(
    () => modelConfig.modelName,
    () => {
      const model = models.value.find((e) => e.name === modelConfig.modelName)
      const classes = model?.classes || []
      if (modelConfig.modelName && !modelConfig.classes[modelConfig.modelName]) {
        modelConfig.classes[modelConfig.modelName] = classes.map((e) => {
          return {
            label: e.label,
            value: e.value,
            selected: true,
          }
        })
      }
    },
    { immediate: true },
  )

  const results = reactive<{ objects: Object2DLine[] | null; isRunning: boolean }>({
    objects: null,
    isRunning: false,
  })

  const runModel = async () => {
    if (!modelConfig.modelName) {
      message.warning('Please choose Model')
      return
    }
    const model = models.value.find((e) => e.name === modelConfig.modelName)
    if (!model) {
      message.warning('Model not found')
      return
    }
    const resultFilterParam = {
      minConfidence: 0.5,
      maxConfidence: 1,
      classes: model?.classes.map((e) => e.value),
    }
    if (!modelConfig.predict) {
      const selectedClasses = (modelConfig.classes[modelConfig.modelName] || []).reduce(
        (classes, item) => {
          if (item.selected) {
            classes.push(item.value)
          }
          return classes
        },
        [] as string[],
      )
      if (selectedClasses.length <= 0) {
        message.warning('Select at least one Class!')
        return
      }
      resultFilterParam.minConfidence = modelConfig.confidence[0]
      resultFilterParam.maxConfidence = modelConfig.confidence[1]
      resultFilterParam.classes = selectedClasses
    }
    const config = {
      datasetId: editor.taskData.taskId,
      dataIds: [+editor.activeFrame.id],
      modelId: +model.id,
      modelVersion: model?.version,
      dataType: 'SINGLE_DATA',
      modelCode: model.code,
      resultFilterParam,
    }
    try {
      results.isRunning = true
      const taskId = await startRunModel(config)
      if (!taskId) throw new Error('Model Run Error')

      pollDataModelResult(taskId)
    } catch (error: any) {
      message.error(error.message || 'Model Run Error')
    }
  }
  const addResults = async () => {
    const { getHeight } = editor.heightMap
    results.objects?.forEach((obj) => {
      editor.utils.createGeometry(
        {
          type: GeometryTypes.Polyline3D as GeometryTypes.Polyline3D,
          frameId: editor.activeFrame.id,
          shape: {
            points: obj.points.map(
              (p) => [p.x, p.y, getHeight(p.x, p.y)] as [number, number, number],
            ),
          },
          sourceType: SourceType.MODEL,
          modelClass: obj.modelClass,
        },
        obj.classId,
      )
    })
    results.objects = null
  }

  async function pollDataModelResult(taskId: string) {
    const frameId = editor.activeFrame.id
    const data = await getModelResult([frameId], taskId)
    const resultList = data.modelDataResults
    const info = resultList[0]
    const { resultFilterParam, modelResult } = info
    if (!modelResult) {
      setTimeout(() => pollDataModelResult(taskId), 1500)
      return
    }
    let objects = modelResult.objects || []
    if (modelResult.code !== 'OK') {
      results.objects = null
      results.isRunning = false
      message.error('model-run-error')
      return
    }
    if (objects.length > 0) {
      objects = objects.filter(
        (e) =>
          e.confidence &&
          e.confidence >= resultFilterParam.minConfidence &&
          e.confidence <= resultFilterParam.maxConfidence,
      )
      results.objects = objects
    } else {
      results.objects = null
      message.warning('model-run-no-data')
    }
    results.isRunning = false
  }

  return {
    modelConfig,
    models,
    isReady,
    results,
    runModel,
    addResults,
  }
})

export default useAIModel
