const en = {
  setting_display: 'Settings',
  setting_imgview: 'Image Display',
  setting_rect: 'Cuboids',
  setting_rect2: 'Multi Cuboids',
  setting_box: '3D Cuboids',
  setting_rect_size: 'Cuboids Size',
  setting_projectbox: 'Projected Cuboids',
  setting_projectpoint: 'Projected Points',
  setting_pointview: 'Points Display',
  setting_backgroundColor: 'Background Color',
  setting_pointsize: 'Size',
  setting_pointreset: 'Reset',
  setting_colorreset: 'Reset Color',
  setting_resultview: 'Objects',
  setting_showlabel: 'Show Tags(M)',
  setting_showannotate: 'Show Annotate(Shift+H)',
  setting_pointcolor: 'Color',
  setting_colorheight: 'Height',
  setting_colorintensity: 'Intensity',
  setting_ground: 'Ground',
  setting_colorvelocity: 'Speed',
  setting_brightness: 'Brightness',
  setting_colorsingle: 'Single',
  setting_img_object: 'Image Object',
  setting_truncation_radio: 'Auto Compute Truncation Radio',
  setting_occlusion_radio: 'Auto Compute Occlusion Radio',

  title_rect: 'Create Rect',
  title_rect2: 'Create Multi Rect',
  title_create2DBox: 'Create Box(2D)',
  title_create3DBox: 'Create Box(3D)',
  title_adjust3DBox: 'Adjust Box(3D)',
  title_translate: 'Translate',
  title_track: 'Track Line',
  title_filter2D: 'Filter other object in 2d view',
  title_overlay: 'Overlay Frame',
  title_model: 'Run Model',
  title_3d_default: 'Manual',
  title_3d_ai: 'AI-assisted',
  title_aux_line: 'Auxiliary Line',
  title_aux_circle: 'Auxiliary Shape',
  title_radius: 'Radius',
  title_contourSetting: 'Contour Setting',
  title_projection: 'Single Frame Projection',
  title_projection_series: 'Serial Frames Projection',
  title_reProjection: 'Re-Projection',

  overlay_title: 'Overlay Frame Setting',
  overlay_range: 'Range',
  overlay_radius: 'Radius(m)',
  overlay_step: 'Step Size',
  overlay_reset: 'Reset',
  overlay_apply: 'Apply',

  model_title: 'AI Annotation Setting',
  model_name: 'Model',
  model_predict: 'Predict all in Model',
  model_select_all: 'Select all',
  model_unselect_all: 'Unselect all',
  model_confidence: 'Confidence',
  model_reset: 'Reset',
  model_classes: 'Classes',
  model_setting: 'Setting',
  model_add: 'Add Results',
  model_run: 'Apply and Run',

  info_title: 'Scene Info',
  info_datainfo: 'Coordinates',
  info_pointinfo: 'Points',
  info_pointall: 'Total Points',
  info_pointvisible: 'Visible Points',

  utility: 'Utility',
  measure: 'Distance Measure(N)',
  measure_add: 'Add a Measure',
  measure_radius: 'Radius(m)',

  btn_msg: 'Info',
  btn_setting: 'Setting',
}
export type ILocale = typeof en
export { en }
