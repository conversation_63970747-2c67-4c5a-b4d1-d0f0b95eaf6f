import { type ILocale } from './type'

const zh: ILocale = {
  setting_display: '设置',
  setting_imgview: '图片显示',
  setting_rect: '矩形框',
  setting_rect2: '多矩形框',
  setting_box: '立体框',
  setting_rect_size: '矩形框长宽',
  setting_projectbox: '映射立体框',
  setting_projectpoint: '映射点',
  setting_pointview: '点云显示',
  setting_backgroundColor: '背景色',
  setting_pointsize: '点大小',
  setting_pointreset: '重置',
  setting_colorreset: '重置颜色',
  setting_resultview: '结果显示',
  setting_showlabel: '显示标签(M)',
  setting_showannotate: '显示批注(Shift+H)',
  setting_pointcolor: '点云颜色',
  setting_colorheight: '高度',
  setting_colorintensity: '强度',
  setting_ground: '地面偏移',
  setting_colorvelocity: '速度',
  setting_brightness: '亮度',
  setting_colorsingle: '单一颜色',
  setting_img_object: '图像对象',
  setting_truncation_radio: '自动计算截断比例',
  setting_occlusion_radio: '自动计算遮挡比例',

  title_rect: '创建矩形',
  title_rect2: '创建多矩形',
  title_create2DBox: '创建立方体(2D)',
  title_create3DBox: '创建立方体(3D)',
  title_adjust3DBox: '调整立方体(3D)',
  title_translate: '移动',
  title_track: '辅助线',
  title_filter2D: '过滤2D显示',
  title_overlay: '叠帧',
  title_model: '跑模型',
  title_3d_default: '手动框',
  title_3d_ai: '智能框',
  title_aux_line: '辅助线',
  title_aux_circle: '辅助圆',
  title_radius: '半径',
  title_contourSetting: '轮廓设置',
  title_projection: '单帧映射',
  title_projection_series: '连续帧映射',
  title_reProjection: '重新映射',

  overlay_title: '叠帧设置',
  overlay_range: '范围',
  overlay_radius: '半径(m)',
  overlay_step: '步长',
  overlay_reset: '重置',
  overlay_apply: '应用',

  model_title: 'AI标注设置',
  model_name: '模型',
  model_predict: '从模型中预测',
  model_select_all: '选择所有',
  model_unselect_all: '反选所有',
  model_confidence: '置信度',
  model_classes: '标签',
  model_setting: '配置',
  model_reset: '重置',
  model_add: '添加结果',
  model_run: '设置并运行',

  info_title: '信息',
  info_datainfo: '数据信息',
  info_pointinfo: '点信息',
  info_pointall: '真实',
  info_pointvisible: '可见',

  utility: '辅助工具',
  measure: '辅助线(N)',
  measure_add: '添加一条辅助线',
  measure_radius: '半径(m)',

  btn_msg: '信息',
  btn_setting: '显示',
}
export { zh }
