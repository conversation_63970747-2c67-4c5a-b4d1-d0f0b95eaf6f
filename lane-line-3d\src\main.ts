import 'ant-design-vue/es/message/style'
import './styles/tailwind.scss'
import './styles/main.scss'

import { createApp } from 'vue'

import axios from '@/api/axios'
import useTaskId from '@/stores/useTaskId'

import App from './App.vue'

axios.defaults.injectCommonPayload = () => {
  const { taskId } = useTaskId()
  return {
    headers: {
      TaskId: taskId,
    },
  }
}

const app = createApp(App)

app.mount('#app')
