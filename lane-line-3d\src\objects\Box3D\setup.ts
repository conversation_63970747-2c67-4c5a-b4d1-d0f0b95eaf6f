import type { Contour } from '@/api/typings/annotation'

import type { IBox3D } from '.'

export const box3DConverter = {
  toRemote: (shape: IBox3D['shape']): Contour => {
    return {
      center3D: { x: shape.center[0], y: shape.center[1], z: shape.center[2] },
      size3D: { x: shape.size[0], y: shape.size[1], z: shape.size[2] },
      rotation3D: { x: shape.rotation[0], y: shape.rotation[1], z: shape.rotation[2] },
    }
  },
  toLocal: (contour: Contour): IBox3D['shape'] => {
    return {
      center: [contour.center3D?.x ?? 0, contour.center3D?.y ?? 0, contour.center3D?.z ?? 0],
      size: [contour.size3D?.x ?? 0, contour.size3D?.y ?? 0, contour.size3D?.z ?? 0],
      rotation: [
        contour.rotation3D?.x ?? 0,
        contour.rotation3D?.y ?? 0,
        contour.rotation3D?.z ?? 0,
      ],
    }
  },
}
