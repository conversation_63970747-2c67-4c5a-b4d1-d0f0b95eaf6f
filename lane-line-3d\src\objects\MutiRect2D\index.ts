import * as THREE from 'three'

import type { GeometryTypes } from '@/const'
import type { IBaseGeometry } from '@/types'

import Object2D from '../base/Object2D'

export interface IMultiRect2DData {
  rects: { center: THREE.Vector2; size: THREE.Vector2 }[]
  viewIndex: number
}
export interface IMultiRect2D<T extends GeometryTypes = any>
  extends IBaseGeometry<T, IMultiRect2DData> {}

export default class MultiRect2D extends Object2D<IMultiRect2D> {
  constructor(userData: IMultiRect2D) {
    super(userData)
  }

  update = (userData: IMultiRect2D) => {
    this.userData = userData
  }

  dispose = () => {}

  render = (ctx: CanvasRenderingContext2D, option: any = {}) => {
    ctx.strokeStyle = '#fff'
    ctx.strokeRect(0, 0, 10000, 10000)
    const rects = this.userData.shape.rects
    if (rects.length === 0) return

    // rects.forEach((rect) => {
    //   drawRect(ctx, rect.center, rect.size)
    // })
    // if (option.showSize) {
    //   drawText(
    //     ctx,
    //     rects[0].center,
    //     rects[0].size,
    //     rects.map(({ size }) => `${size.x.toFixed(0)}*${size.y.toFixed(0)}`).join('/'),
    //   )
    // }
  }
}

const temp_1 = new THREE.Vector2()
const temp_2 = new THREE.Vector2()

function drawRect(ctx: CanvasRenderingContext2D, center: THREE.Vector2, size: THREE.Vector2) {
  ctx.save()
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  ctx.lineWidth = 1
  ctx.strokeStyle = '#fff'
  temp_1.copy(size).multiplyScalar(0.5)
  temp_2.copy(center).sub(temp_1)
  ctx.strokeRect(temp_2.x, temp_2.y, size.x, size.y)
  ctx.restore()
}

function drawText(
  ctx: CanvasRenderingContext2D,
  center: THREE.Vector2,
  size: THREE.Vector2,
  text: string,
) {
  ctx.save()
  const trans = ctx.getTransform()
  const transform = (pos: THREE.Vector2) => {
    return pos
      .clone()
      .multiply(new THREE.Vector2(trans.a, trans.d))
      .add(new THREE.Vector2(trans.e, trans.f))
  }
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  const fontSize = 12
  const padding = new THREE.Vector2(4, 3)
  const offset = new THREE.Vector2(5, -5)
  ctx.font = `${fontSize}px Arial`
  /** 左上角顶点 */
  const tlPos = transform(new THREE.Vector2(center.x - size.x / 2, center.y - size.y / 2))
  const textMetrics = ctx.measureText(text)
  ctx.fillStyle = 'rgba(0,0,0,0.7)'
  const lineHeight = fontSize + padding.y * 2
  ctx.fillRect(
    tlPos.x + offset.x,
    tlPos.y - lineHeight + offset.y,
    textMetrics.width + padding.x * 2,
    lineHeight,
  )
  ctx.fillStyle = '#fff'
  ctx.fillText(text, tlPos.x + padding.x + offset.x, tlPos.y - padding.y + offset.y)
  ctx.restore()
}
