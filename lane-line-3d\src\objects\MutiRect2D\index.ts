import * as THREE from 'three'

import type { GeometryTypes } from '@/const'
import type { IBaseGeometry } from '@/types'

import Object2D from '../base/Object2D'

export interface IMultiRect2DData {
  rects: { center: THREE.Vector2; size: THREE.Vector2 }[]
}
export interface IMultiRect2D<T extends GeometryTypes = any>
  extends IBaseGeometry<T, IMultiRect2DData> {}

export default class MultiRect2D extends Object2D<IMultiRect2D> {
  constructor(userData: IMultiRect2D) {
    super(userData)
  }

  update = (userData: IMultiRect2D) => {
    this.userData = userData
  }

  dispose = () => {}

  render = (ctx: CanvasRenderingContext2D, option: { showSize?: boolean } = {}) => {
    const rects = this.userData.shape.rects
    if (rects.length === 0) return

    rects.forEach((rect) => {
      drawRect(ctx, rect.center, rect.size)
    })
    if (option.showSize) {
      // 在第一个矩形上显示所有矩形的尺寸
      drawText(
        ctx,
        rects[0].center,
        rects[0].size,
        rects.map(({ size }) => `${size.x.toFixed(0)}*${size.y.toFixed(0)}`).join('/'),
      )
    }
  }
}

function drawRect(ctx: CanvasRenderingContext2D, center: THREE.Vector2, size: THREE.Vector2) {
  ctx.save()

  // 获取当前变换矩阵，计算缩放因子
  const trans = ctx.getTransform()
  // 计算x和y方向的缩放因子，取较小值确保线宽在任何方向都是1像素
  const scaleX = Math.abs(trans.a)
  const scaleY = Math.abs(trans.d)
  const scale = Math.min(scaleX, scaleY)

  // 设置lineWidth为1除以缩放因子，这样实际渲染时就是1像素
  ctx.lineWidth = 1 / scale
  ctx.strokeStyle = '#fff'
  ctx.strokeRect(center.x - size.x / 2, center.y - size.y / 2, size.x, size.y)
  ctx.restore()
}

function drawText(
  ctx: CanvasRenderingContext2D,
  center: THREE.Vector2,
  size: THREE.Vector2,
  text: string,
) {
  ctx.save()
  const trans = ctx.getTransform()
  const transform = (pos: THREE.Vector2) => {
    return pos
      .clone()
      .multiply(new THREE.Vector2(trans.a, trans.d))
      .add(new THREE.Vector2(trans.e, trans.f))
  }
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  const fontSize = 12
  const padding = new THREE.Vector2(4, 3)
  const offset = new THREE.Vector2(5, -5)
  ctx.font = `${fontSize}px Arial`
  /** 左上角顶点 */
  const tlPos = transform(new THREE.Vector2(center.x - size.x / 2, center.y - size.y / 2))
  const textMetrics = ctx.measureText(text)
  ctx.fillStyle = 'rgba(0,0,0,0.7)'
  const lineHeight = fontSize + padding.y * 2
  ctx.fillRect(
    tlPos.x + offset.x,
    tlPos.y - lineHeight + offset.y,
    textMetrics.width + padding.x * 2,
    lineHeight,
  )
  ctx.fillStyle = '#fff'
  ctx.fillText(text, tlPos.x + padding.x + offset.x, tlPos.y - padding.y + offset.y)
  ctx.restore()
}
