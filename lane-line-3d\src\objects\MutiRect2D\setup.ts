import * as THREE from 'three'

import type { Contour } from '@/api/typings/annotation'

import type { IMultiRect2DData } from '.'

export const multiRect2DConverter = {
  toRemote: (shape: IMultiRect2DData): Contour => {
    return {
      points: shape.rects.map((rect) => rectToPoints(rect.center, rect.size)).flat(),
    }
  },
  toLocal: (contour: Contour): IMultiRect2DData => {
    const rects: IMultiRect2DData['rects'] = []
    if (contour.points) {
      for (let i = 0; i < contour.points.length; i += 4) {
        const points = contour.points.slice(i, i + 4)
        if (points.length < 4) continue
        const rect = getBoxRect(points.map((p) => new THREE.Vector2(p.x, p.y)))
        rects.push(rect)
      }
    }
    return {
      rects,
    }
  },
}

function getBoxRect(points: THREE.Vector2[]) {
  const bbox = getBBox(points)
  return {
    center: new THREE.Vector2((bbox.xMax + bbox.xMin) / 2, (bbox.yMax + bbox.yMin) / 2),
    size: new THREE.Vector2(bbox.xMax - bbox.xMin, bbox.yMax - bbox.yMin),
  }
}

function getBBox(points: THREE.Vector2[]) {
  let xMin = Infinity
  let xMax = -Infinity
  let yMin = Infinity
  let yMax = -Infinity
  points.forEach((p) => {
    if (p.x < xMin) xMin = p.x
    if (p.x > xMax) xMax = p.x
    if (p.y > yMax) yMax = p.y
    if (p.y < yMin) yMin = p.y
  })
  return { xMax, xMin, yMax, yMin }
}

function rectToPoints(center: THREE.Vector2, size: THREE.Vector2) {
  return [
    new THREE.Vector2(center.x - size.x / 2, center.y - size.y / 2),
    new THREE.Vector2(center.x - size.x / 2, center.y + size.y / 2),
    new THREE.Vector2(center.x + size.x / 2, center.y + size.y / 2),
    new THREE.Vector2(center.x + size.x / 2, center.y - size.y / 2),
  ]
}
