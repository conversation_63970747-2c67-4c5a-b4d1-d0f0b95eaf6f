import * as THREE from 'three'

import type { GeometryTypes } from '@/const'
import type { IBaseGeometry } from '@/types'

export interface IPoint3DData {
  point: [number, number, number]
}
export interface IPoint3D<T extends GeometryTypes = any> extends IBaseGeometry<T, IPoint3DData> {}

export default class Point3D extends THREE.Points {
  static readonly DEFAULT_COLOR = '#fff'
  static readonly SELECTED_COLOR = '#ff0000'
  static readonly BORDER_COLOR = '#2c8fff'

  static readonly POINT_SIZE = 10

  static readonly POINT_MATERIAL = createPointMaterial(
    Point3D.POINT_SIZE,
    Point3D.DEFAULT_COLOR,
    Point3D.BORDER_COLOR,
  )
  static readonly SELECTED_POINT_MATERIAL = createPointMaterial(
    Point3D.POINT_SIZE,
    Point3D.SELECTED_COLOR,
    Point3D.BORDER_COLOR,
  )

  static readonly POINT_GEOMETRY = createPointGeometry()

  constructor(userData: IPoint3D) {
    super(
      Point3D.POINT_GEOMETRY,
      userData.selected ? Point3D.SELECTED_POINT_MATERIAL : Point3D.POINT_MATERIAL,
    )
    const [x, y, z] = userData.shape.point
    this.position.set(x, y, z)
    this.userData = userData
  }

  update(userData: IPoint3D) {
    this.userData = userData
    const [x, y, z] = userData.shape.point
    this.position.set(x, y, z)
    this.material = userData.selected ? Point3D.SELECTED_POINT_MATERIAL : Point3D.POINT_MATERIAL
  }

  dispose() {}
}

function drawCircle(
  context: CanvasRenderingContext2D,
  size: number,
  color = '#fff',
  borderColor = color,
) {
  context.save()
  context.beginPath()
  context.arc(size, size, size, 0, Math.PI * 2, false)
  context.fillStyle = borderColor
  context.fill()
  context.beginPath()
  context.arc(size, size, size * 0.6, 0, Math.PI * 2, false)
  context.fillStyle = color
  context.fill()
  context.restore()
}

export function createPointGeometry() {
  const geometry = new THREE.BufferGeometry()
  geometry.setAttribute('position', new THREE.Float32BufferAttribute([0, 0, 0], 3))
  return geometry
}

export function createPointMaterial(size: number, color: string, borderColor = color) {
  const canvas = document.createElement('canvas')
  canvas.width = size
  canvas.height = size

  const context = canvas.getContext('2d')!

  drawCircle(context, size / 2, color, borderColor)

  const texture = new THREE.Texture(canvas)
  texture.needsUpdate = true

  return new THREE.PointsMaterial({
    map: texture,
    size,
    sizeAttenuation: false,
    depthTest: false,
    transparent: true,
  })
}

export { setupPoint3DFeature } from './setup'
