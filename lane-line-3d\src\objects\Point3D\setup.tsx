import { message } from 'ant-design-vue'
import * as THREE from 'three'
import { h, render } from 'vue'

import type { Contour } from '@/api/typings/annotation'
import { GeometryTypes } from '@/const'
import useEditor, { EditorEvent } from '@/editors/LaneLine3D/useEditor'
import ToolbarItem from '@/layouts/TheToolbar/ToolbarItem.vue'
import useSelection from '@/stores/useSelection'
import PointCloudMainViewController from '@/views/PointCloudMainView/PointCloudMainViewController'
import EditPoint from '@/views/PointCloudSideView/components/EditPoint.vue'
import PointCloudSideViewController from '@/views/PointCloudSideView/PointCloudSideViewController'

import { setupMainConnectPointsInteraction } from '../Polyline3D/setup'
import type { IPoint3D } from '.'

export function setupPoint3DFeature(editor: ReturnType<typeof useEditor>) {
  editor.on(EditorEvent.ViewMounted, ({ controller }) => {
    if (controller instanceof PointCloudMainViewController) {
      const mainConnectPointsInteraction = setupMainConnectPointsInteraction(editor, controller)
      const ACTION_NAME = 'create3DPoint'
      editor.interactionManager.useRegisterInteraction(ACTION_NAME, {
        execute: () => mainConnectPointsInteraction.execute({ maxPoints: 1 }),
        stop: mainConnectPointsInteraction.stop,
      })
      const create3DPoint = () => {
        const selection = useSelection()
        editor.interactionManager.executeInteraction(ACTION_NAME).then(
          (points: THREE.Vector3[]) => {
            const p = points[0]
            const geometry = editor.utils.createGeometryInInteraction({
              type: GeometryTypes.Point3D,
              shape: {
                point: [p.x, p.y, p.z],
              },
            })
            selection.selectGeometryById(geometry.id)
          },
          (err) => {
            if (!err) return
            message.error(err instanceof Error ? err.message : err)
          },
        )
      }
      editor.toolbar.create3DPoint = () => (
        <ToolbarItem
          active={editor.interactionManager.currentInteraction?.name === ACTION_NAME}
          name="创建点"
          onClick={create3DPoint}
        >
          {{
            icon: () => <i class="iconfont icon-point" />,
          }}
        </ToolbarItem>
      )

      Object.assign(editor.methods, {
        create3DPoint,
      })
    }
    if (controller instanceof PointCloudSideViewController) {
      if (!controller.userData.pointSimulation) {
        const div = document.createElement('div')
        const vnode = h(EditPoint, { controller })
        render(vnode, div)
        controller.container?.appendChild(div)
        controller.userData.pointSimulation = div
      }
    }
  })
}

export const point3DConverter = {
  toRemote: (shape: IPoint3D['shape']) => {
    return {
      points: [shape.point].map((p) => ({ x: p[0], y: p[1], z: p[2] })),
    }
  },
  toLocal: (contour: Contour) => {
    return {
      point: contour.points?.map((p) => [p.x, p.y, p.z] as [number, number, number])?.[0] ?? [
        0, 0, 0,
      ],
    }
  },
}
