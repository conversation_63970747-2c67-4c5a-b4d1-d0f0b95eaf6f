import type { Contour } from '@/api/typings/annotation'
import { GeometryTypes } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'

import { setupPolylineFeatureHelper } from '../Polyline3D/setup'
import type { IPolygon3D } from '.'

export function setupPolygon3DFeature(editor: ReturnType<typeof useEditor>) {
  setupPolylineFeatureHelper(
    editor,
    GeometryTypes.Polygon3D,
    'create3DPolygon',
    '创建多边形',
    'icon-polygon-v1',
  )
}

export const polygon3DConverter = {
  toRemote: (shape: IPolygon3D['shape']) => {
    return {
      points: shape.points.map((p) => ({ x: p[0], y: p[1], z: p[2] })),
    }
  },
  toLocal: (contour: Contour) => {
    return {
      points: contour.points?.map((p) => [p.x, p.y, p.z] as [number, number, number]) ?? [],
    }
  },
}
