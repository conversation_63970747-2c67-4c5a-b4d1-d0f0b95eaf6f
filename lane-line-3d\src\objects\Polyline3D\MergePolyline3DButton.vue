<script setup lang="ts">
import { GeometryTypes } from '@/const'
import { useEditor } from '@/editors/LaneLine3D/useEditor'
import useSelection from '@/stores/useSelection'

import type { IPolyline3D } from '.'

const props = defineProps<{
  object: IPolyline3D
  source: IPolyline3D
}>()

const editor = useEditor()
const selection = useSelection()

const handleClick = () => {
  editor.utils.updateGeometryById(props.object.id, (geometry) => {
    if (geometry.type === GeometryTypes.Polyline3D) {
      geometry.shape.points.push(...props.source.shape.points)
    }
  })
  editor.utils.deleteGeometryById(props.source.id)
  selection.selectGeometryById(props.object.id)
}
</script>

<template>
  <div
    class="gap-0.2 flex cursor-pointer flex-col items-center justify-center rounded bg-slate-950 p-1 text-[10px] text-gray-300 hover:bg-[#224b77]"
    title="合并"
    @click="handleClick"
  >
    <i class="iconfont icon-bone" />
    <span>保留</span>
    <span>{{ object.id.slice(-4) }}</span>
  </div>
</template>
