<script setup lang="ts">
import type { IPolyline3D } from '.'
import MergePolyline3DButton from './MergePolyline3DButton.vue'

const props = defineProps<{
  object1: IPolyline3D
  object2: IPolyline3D
}>()
</script>

<template>
  <div class="flex flex-col gap-1">
    <MergePolyline3DButton
      :object="props.object1"
      :source="props.object2"
    />
    <MergePolyline3DButton
      :object="props.object2"
      :source="props.object1"
    />
  </div>
</template>
