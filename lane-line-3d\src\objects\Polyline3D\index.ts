import * as THREE from 'three'

import type { GeometryTypes } from '@/const'
import type { IBaseGeometry } from '@/types'

import { createPointGeometry, createPointMaterial } from '../Point3D'

export { setupPolyline3DFeature } from './setup'
export interface IPolyline3DData {
  points: [number, number, number][]
}
export interface IPolyline3D<T extends GeometryTypes = any>
  extends IBaseGeometry<T, IPolyline3DData> {}

export default class Polyline3D extends THREE.Line {
  static readonly DEFAULT_COLOR = '#fff'
  static readonly SELECTED_COLOR = '#ff0000'

  static readonly LINE_MATERIAL = createLineMaterial(Polyline3D.DEFAULT_COLOR)
  static readonly SELECTED_LINE_MATERIAL = createLineMaterial(Polyline3D.SELECTED_COLOR)

  static readonly NODE_SIZE = 5
  static readonly NODE_GEOMETRY = createPointGeometry()
  static readonly NODE_MATERIAL = createPointMaterial(
    Polyline3D.NODE_SIZE,
    Polyline3D.DEFAULT_COLOR,
  )
  static readonly SELECTED_NODE_MATERIAL = createPointMaterial(
    Polyline3D.NODE_SIZE,
    Polyline3D.SELECTED_COLOR,
  )

  points: THREE.Vector3[]

  nodeGroup: THREE.Group
  arrow: Arrow3D

  /** 是否闭合 */
  close: boolean

  constructor(userData: IPolyline3D, close = false) {
    const points = userData.shape.points.map((p) => new THREE.Vector3(p[0], p[1], p[2]))
    const geometry = new THREE.BufferGeometry()
    super(
      geometry,
      userData.selected ? Polyline3D.SELECTED_LINE_MATERIAL : Polyline3D.LINE_MATERIAL,
    )
    this.userData = userData
    this.close = close
    this.points = points
    this.updateGeometry()

    this.nodeGroup = new THREE.Group()
    this.updateNodeGroup()
    this.add(this.nodeGroup)

    this.arrow = new Arrow3D()
    this.updateArrow()
    this.add(this.arrow)
  }

  updateGeometry = () => {
    this.geometry.setFromPoints([...this.points, ...(this.close ? this.points.slice(0, 1) : [])])
    this.geometry.computeBoundingBox()
  }

  private createNode = () => {
    const node = new THREE.Points(Polyline3D.NODE_GEOMETRY, Polyline3D.NODE_MATERIAL)
    node.renderOrder = 100
    return node
  }

  private updateNodeGroup = () => {
    const diff = this.points.length - this.nodeGroup.children.length
    if (diff > 0) {
      for (let i = 0; i < diff; i++) {
        const node = this.createNode()
        this.nodeGroup.add(node)
      }
    } else if (diff < 0) {
      for (let i = 0; i < Math.abs(diff); i++) {
        this.nodeGroup.children.pop()!
      }
    }
    this.nodeGroup.children.forEach((item, index) => {
      const node = item as THREE.Points
      node.position.copy(this.points[index])
      node.material = this.userData.selected
        ? Polyline3D.SELECTED_NODE_MATERIAL
        : Polyline3D.NODE_MATERIAL
    })
  }

  private updateArrow = () => {
    const [start, end] = this.points

    if (start && end) {
      this.arrow.visible = true
      this.arrow.quaternion
        .setFromUnitVectors(
          new THREE.Vector3(1, 0, 0),
          new THREE.Vector3().copy(end).sub(start).normalize(),
        )
        .multiply(this.quaternion)

      this.arrow.position.copy(start).add(end).multiplyScalar(0.5).add(this.position)
      this.arrow.updateMatrixWorld()
      this.arrow.material = this.material
    } else {
      this.arrow.visible = false
    }
  }

  update = (userData: IPolyline3D) => {
    const points = userData.shape.points.map((p) => new THREE.Vector3(p[0], p[1], p[2]))
    this.userData = userData
    this.points = points

    this.material = userData.selected ? Polyline3D.SELECTED_LINE_MATERIAL : Polyline3D.LINE_MATERIAL

    this.updateGeometry()
    this.updateNodeGroup()
    this.updateArrow()
  }

  dispose = () => {
    this.removeFromParent()
  }
}

function createLineMaterial(color: string) {
  return new THREE.LineBasicMaterial({
    color,
    linewidth: 1,
    depthTest: false,
  })
}

export class Arrow3D extends THREE.Line {
  static readonly DEFAULT_MATERIAL = new THREE.LineBasicMaterial({
    color: 0xffffff,
    side: THREE.FrontSide,
  })
  static readonly DEFAULT_GEOMETRY = (() => {
    const geometry = new THREE.BufferGeometry()
    const positions = [0, 0, 0, -1, 1, 0, 1, 0, 0, -1, -1, 0, 0, 0, 0]
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3))
    return geometry
  })()

  constructor(geometry?: THREE.BufferGeometry | null, material?: THREE.Material) {
    super(geometry ?? Arrow3D.DEFAULT_GEOMETRY, material ?? Arrow3D.DEFAULT_MATERIAL)
    this.scale.set(0.3, 0.3, 1)
  }
  dispose() {}
}
