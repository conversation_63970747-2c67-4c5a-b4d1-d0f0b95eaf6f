import { message, Tooltip } from 'ant-design-vue'
import * as THREE from 'three'
import { h, render, watch } from 'vue'

import type { Contour } from '@/api/typings/annotation'
import { GeometryTypes } from '@/const'
import useEditor, { EditorEvent } from '@/editors/LaneLine3D/useEditor'
import ConnectPointsInteraction from '@/interactions/ConnectPointsInteraction'
import ToolbarItem from '@/layouts/TheToolbar/ToolbarItem.vue'
import useActionManager from '@/stores/useActionManager'
import useSelection from '@/stores/useSelection'
import { canvasToWorld, worldToCanvas } from '@/utils/three-utils'
import PolylineSimulation from '@/views/PointCloudMainView/components/PolylineSimulation.vue'
import usePolylinePointSelection from '@/views/PointCloudMainView/hooks/usePolylinePointSelection'
import PointCloudMainViewController from '@/views/PointCloudMainView/PointCloudMainViewController'
import EditPolylinePoint from '@/views/PointCloudSideView/components/EditPolylinePoint.vue'
import PointCloudSideViewController from '@/views/PointCloudSideView/PointCloudSideViewController'

import Polyline3D, { type IPolyline3D } from '.'
import MergePolyline3DButtonGroup from './MergePolyline3DButtonGroup.vue'

export function setupPolyline3DFeature(editor: ReturnType<typeof useEditor>) {
  setupPolylineFeatureHelper(
    editor,
    GeometryTypes.Polyline3D,
    'create3DPolyline',
    '创建折线',
    'icon-polyline-v1',
  )
}

export function setupMainConnectPointsInteraction(
  editor: ReturnType<typeof useEditor>,
  controller: PointCloudMainViewController,
) {
  let connectPointsInteraction: ConnectPointsInteraction<THREE.Vector3> =
    controller.userData.connectPointsInteraction
  if (!connectPointsInteraction) {
    connectPointsInteraction = new ConnectPointsInteraction(
      controller.container!,
      controller.renderer.domElement,
      {
        clientToWorld: (p: { x: number; y: number }) => {
          return canvasToWorld(
            controller.renderer.domElement,
            controller.camera,
            new THREE.Vector2(p.x, p.y),
            editor.heightMap.object,
          )
        },
        worldToClient: (p: THREE.Vector3) => {
          return worldToCanvas(controller.renderer.domElement, controller.camera, p)
        },
      },
    )
    controller.controls.addEventListener('change', () => {
      connectPointsInteraction.render()
    })
    const actionManager = useActionManager()
    const actionMap = {
      esc: () => {
        connectPointsInteraction.stop()
      },
      enter: () => {
        connectPointsInteraction.finishCurrentDraw()
      },
      undo: () => {
        connectPointsInteraction.undoDraw()
      },
      redo: () => {},
      del: () => {},
      save: () => {},
    }
    for (const [name, callback] of Object.entries(actionMap)) {
      actionManager.useRegisterAction(name, (next) => {
        if (connectPointsInteraction.promise) {
          callback()
        } else {
          next()
        }
      })
    }
    controller.userData.connectPointsInteraction = connectPointsInteraction
  }
  return connectPointsInteraction
}

export function setupPolylineFeatureHelper(
  editor: ReturnType<typeof useEditor>,
  geometryType: GeometryTypes.Polyline3D | GeometryTypes.Polygon3D,
  actionName: string,
  toolName: string,
  iconClass: string,
) {
  editor.on(EditorEvent.ViewMounted, ({ controller }) => {
    if (controller instanceof PointCloudMainViewController) {
      const mainConnectPointsInteraction = setupMainConnectPointsInteraction(editor, controller)
      const params =
        geometryType === GeometryTypes.Polygon3D
          ? { minPoints: 3, fill: true, trackLine: true }
          : { minPoints: 2 }
      editor.interactionManager.useRegisterInteraction(actionName, {
        execute: () => mainConnectPointsInteraction.execute(params),
        stop: mainConnectPointsInteraction.stop,
      })
      const create = () => {
        const selection = useSelection()
        editor.interactionManager.executeInteraction(actionName).then(
          (points: THREE.Vector3[]) => {
            const geometry = editor.utils.createGeometryInInteraction({
              type: geometryType,
              shape: {
                points: points.map((p) => [p.x, p.y, p.z]),
              },
            })
            selection.selectGeometryById(geometry.id)
          },
          (err) => {
            if (!err) return
            message.error(err instanceof Error ? err.message : err)
          },
        )
      }
      const renderToolbarItem = () => (
        <ToolbarItem
          active={editor.interactionManager.currentInteraction?.name === actionName}
          name={toolName}
          onClick={create}
        >
          {{
            icon: () => <i class={`iconfont ${iconClass}`} />,
          }}
        </ToolbarItem>
      )
      const renderToolbarItemWithTooltip = () => {
        const { selectedGeometries } = useSelection()
        const visible =
          selectedGeometries.value.length === 2 &&
          selectedGeometries.value.every((item) => item.type === GeometryTypes.Polyline3D)
        return (
          <Tooltip
            placement="left"
            overlayClassName="tool-extra-tooltip"
            visible={visible}
          >
            {{
              default: renderToolbarItem,
              title: () =>
                visible ? (
                  <MergePolyline3DButtonGroup
                    object1={selectedGeometries.value[0]}
                    object2={selectedGeometries.value[1]}
                  />
                ) : null,
            }}
          </Tooltip>
        )
      }
      editor.toolbar[actionName] =
        geometryType === GeometryTypes.Polyline3D ? renderToolbarItemWithTooltip : renderToolbarItem

      Object.assign(editor.methods, {
        [actionName]: create,
      })

      if (!controller.userData.polylineSimulation) {
        const div = document.createElement('div')
        const vnode = h(PolylineSimulation, { controller })
        render(vnode, div)
        controller.container?.appendChild(div)
        controller.userData.polylineSimulation = div
      }
    }
    if (controller instanceof PointCloudSideViewController) {
      if (!controller.userData.editPolylinePoint) {
        const div = document.createElement('div')
        const vnode = h(EditPolylinePoint, { controller })
        render(vnode, div)
        controller.container?.appendChild(div)
        controller.userData.editPolylinePoint = div
        watch(
          () => usePolylinePointSelection().selectedPointIndex.value,
          (index) => {
            const o = controller.targetObject
            if (o && o instanceof Polyline3D) {
              controller.setTargetObject(o, o.nodeGroup.children[index])
              controller.fitObject()
            }
          },
        )
      }
    }
  })
}

export const polyline3DConverter = {
  toRemote: (shape: IPolyline3D['shape']) => {
    return {
      points: shape.points.map((p) => ({ x: p[0], y: p[1], z: p[2] })),
    }
  },
  toLocal: (contour: Contour) => {
    return {
      points: contour.points?.map((p) => [p.x, p.y, p.z] as [number, number, number]) ?? [],
    }
  },
}
