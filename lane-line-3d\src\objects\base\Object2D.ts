import * as THREE from 'three'

export default class Object2D<
  T extends { [key: string]: any } = any,
> extends THREE.EventDispatcher {
  uuid = THREE.MathUtils.generateUUID()
  userData: T
  constructor(userData: T) {
    super()
    this.userData = userData
  }

  update = (userData: T) => {
    this.userData = userData
  }

  dispose = () => {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  render = (context: CanvasRenderingContext2D) => {}
}
