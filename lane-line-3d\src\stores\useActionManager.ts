import { createGlobalState } from '@vueuse/core'
import { pull } from 'lodash-es'
import { onScopeDispose, reactive } from 'vue'

/**
 * 用于管理全局动作，如 esc、enter 等。
 * 当触发动作时，会按照注册顺序倒序执行所有注册的回调函数。
 */
const useActionManager = createGlobalState(() => {
  const actions = reactive<Record<string, Array<(next: () => void) => void>>>({})
  const useRegisterAction = (name: string, callback: (next: () => void) => void) => {
    if (!actions[name]) actions[name] = []
    actions[name].push(callback)
    onScopeDispose(() => {
      pull(actions[name], callback)
    })
  }
  const triggerAction = (name: string) => {
    if (!actions[name] || actions[name].length === 0) return

    const executeChain = (index: number) => {
      if (index < 0) return

      const currentCallback = actions[name][index]

      currentCallback(() => {
        // 调用链中的下一个action（按注册顺序是前一个）
        executeChain(index - 1)
      })
    }

    // 从最后注册的action开始执行
    executeChain(actions[name].length - 1)
  }
  return {
    useRegisterAction,
    triggerAction,
  }
})

export default useActionManager
