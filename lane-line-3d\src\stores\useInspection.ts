import { createGlobalState } from '@vueuse/core'
import { computed, ref, watch } from 'vue'

import { useEditor } from '@/editors/LaneLine3D/useEditor'

import useSelection from './useSelection'

/**
 * 用于管理批注详情的显示。
 * 目标可以是 trackObject 也可以是 geometry。
 */
const useInspection = createGlobalState(() => {
  const editor = useEditor()
  const { selectedTrackObjects, selectedGeometries } = useSelection()
  const inspectedTrackObjectId = ref('')
  const inspectedGeometryId = ref('')
  const inspectedTrackObject = computed(() => {
    return editor.trackObjects.find((item) => item.id === inspectedTrackObjectId.value) || null
  })
  const inspectedGeometry = computed(() => {
    return editor.geometries.find((item) => item.id === inspectedGeometryId.value) || null
  })
  const isInspecting = ref(false)

  const clear = () => {
    inspectedTrackObjectId.value = ''
    inspectedGeometryId.value = ''
    isInspecting.value = false
  }

  watch(
    () =>
      !!inspectedTrackObjectId.value &&
      !editor.trackObjects.find((item) => item.id === inspectedTrackObjectId.value),
    (removed) => {
      if (removed) {
        clear()
      }
    },
  )
  watch(
    () =>
      !!inspectedGeometryId.value &&
      !editor.geometries.find((item) => item.id === inspectedGeometryId.value),
    (removed) => {
      if (removed) {
        clear()
      }
    },
  )
  watch(
    () => [selectedTrackObjects.value, selectedGeometries.value],
    ([trackObjects, geometries]) => {
      if (trackObjects.length !== 1) {
        clear()
      } else {
        inspectedTrackObjectId.value = trackObjects[0].id
        inspectedGeometryId.value = geometries.length === 1 ? geometries[0].id : ''
      }
    },
  )

  return {
    isInspecting,
    inspectedTrackObjectId,
    inspectedTrackObject,
    inspectedGeometryId,
    inspectedGeometry,
  }
})

export default useInspection
