import { createGlobalState } from '@vueuse/core'
import { onScopeDispose, shallowReactive, shallowRef } from 'vue'

export interface IInteraction {
  name: string
  execute: (params?: any) => Promise<any>
  stop: () => void | Promise<void>
  valid?: () => boolean
}

/**
 * 用于管理交互状态，同一时间只能有一个交互状态。
 */
const useInteractionManager = createGlobalState(() => {
  const interactionMap = shallowReactive<Record<string, IInteraction>>({})
  const currentInteraction = shallowRef<IInteraction | null>(null)

  function useRegisterInteraction(name: string, action: Omit<IInteraction, 'name'>) {
    if (interactionMap[name]) return
    interactionMap[name] = {
      ...action,
      name,
    }
    onScopeDispose(() => {
      delete interactionMap[name]
    })
  }

  async function executeInteraction<T extends string>(name: T, params?: any): Promise<any> {
    const action = interactionMap[name]

    if (!action) return

    stopCurrentAction()

    await new Promise<void>((r) => setTimeout(r, 0)) // 确保上一次的 execute 和 action.execute 里的 finally 已经执行

    let result
    if (!action.valid || action.valid()) {
      currentInteraction.value = action
      try {
        result = await action.execute(params)
      } finally {
        currentInteraction.value = null
      }
    }

    return result
  }

  function stopCurrentAction() {
    if (!currentInteraction.value) return

    currentInteraction.value.stop()
    currentInteraction.value = null
  }

  return {
    currentInteraction,
    useRegisterInteraction,
    executeInteraction,
  }
})

export default useInteractionManager
