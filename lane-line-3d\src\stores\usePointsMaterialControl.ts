import { createGlobalState } from '@vueuse/core'
import { isNumber } from 'lodash-es'
import * as THREE from 'three'
import { reactive, watch } from 'vue'

import PointsMaterial, { type UniformsOption } from '../hooks/usePointCloud/PointsMaterial'

const getDefaultConfig = () => {
  const DEFAULT_UNIFORMS = PointsMaterial.getDefaultUniforms()
  return {
    pointSize: DEFAULT_UNIFORMS.pointSize,
    /* 亮度 */

    brightness: DEFAULT_UNIFORMS.brightness,

    /* 点云颜色模式 */
    colorMode: DEFAULT_UNIFORMS.colorMode,
    /**
     * 点云颜色在高度模式下，
     * 该高度范围的点的颜色会根据高度变化。
     */
    pointHeight: [DEFAULT_UNIFORMS.pointHeight.x, DEFAULT_UNIFORMS.pointHeight.y] as [
      number,
      number,
    ],
    /**
     * 点云颜色在高度模式下，
     * 小于或大于 pointHeight 的点分别使用第一个和第二个颜色
     */
    edgeColor: DEFAULT_UNIFORMS.edgeColor.map((color) => `#${color.getHexString()}`) as [
      string,
      string,
    ],
    /* 点云颜色在单一颜色模式下，使用该颜色 */
    singleColor: `#${DEFAULT_UNIFORMS.singleColor.getHexString()}`,
    /* 是否开启强度过滤 */
    openIntensity: DEFAULT_UNIFORMS.openIntensity === 1,
    /* 开启强度过滤后，根据强度范围过滤 */
    intensityRange: [DEFAULT_UNIFORMS.intensityRange.x, DEFAULT_UNIFORMS.intensityRange.y] as [
      number,
      number,
    ],

    /* 根据高度范围过滤 */
    heightRange: [DEFAULT_UNIFORMS.heightRange.x, DEFAULT_UNIFORMS.heightRange.y] as [
      number,
      number,
    ],
  }
}

const rangeToVector2 = (range: [number, number]) => new THREE.Vector2(range[0], range[1])
const isValidRange = (range: [number, number]) => {
  return range.every((value) => isNumber(value) && !isNaN(value))
}
export const configToUniforms = (config: ReturnType<typeof getDefaultConfig>) => {
  const option: UniformsOption = {
    pointSize: config.pointSize,
    colorMode: config.colorMode || 0,
    openIntensity: config.openIntensity ? 1.0 : -1.0,
    brightness: config.brightness,
    intensityRange: rangeToVector2(config.intensityRange),
    edgeColor: config.edgeColor.map((color) => new THREE.Color(color)) as [
      THREE.Color,
      THREE.Color,
    ],
    singleColor: new THREE.Color(config.singleColor),
  }
  if (isValidRange(config.pointHeight)) {
    option.pointHeight = rangeToVector2(config.pointHeight)
  }
  if (isValidRange(config.heightRange)) {
    /**
     * @todo 更新 vCount
     */
    option.heightRange = rangeToVector2(config.heightRange)
  }
  return option
}

/**
 * 用于控制点云的显示效果
 */
export const usePointsMaterialControl = createGlobalState(() => {
  const config = reactive(getDefaultConfig())
  let target: PointsMaterial
  let onUpdate: () => void
  const attach = (m: PointsMaterial, fn: () => void) => {
    target = m
    onUpdate = fn
  }
  watch(
    () => config,
    () => {
      target?.setUniforms(configToUniforms(config))
      onUpdate?.()
    },
    {
      deep: true,
    },
  )
  return {
    config,
    attach,
  }
})

export default usePointsMaterialControl
