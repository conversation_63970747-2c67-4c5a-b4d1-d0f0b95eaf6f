import { createGlobalState } from '@vueuse/core'
import { isEqual } from 'lodash-es'

import type { DataAnnotation, DataInfo, ObjectItem } from '@/api/typings/annotation'
import { localFramesDataToRemote } from '@/utils/classification'

import { useEditor } from '../editors/LaneLine3D/useEditor'
import { localTrackObjectsToRemote } from '../utils/convertObjects'

/**
 * 用于跟踪保存数据的变化，生成补丁数据。
 */
const useSaveDataChangeTracker = createGlobalState(() => {
  const editor = useEditor()
  /** 初始加载和每次保存成功后更新的数据快照 */
  const snapshots: Record<string, { objects: ObjectItem[]; dataAnnotations: DataAnnotation[] }> = {}
  /**
   * 比较每一帧的 objects 和 classifications，
   * 存在不同则添加该帧，不同的 object 会更新 version，
   * 生成补丁数据，用于提交。
   */
  const getChangedFrames = () => {
    const newObjectsMap = localTrackObjectsToRemote(editor.trackObjects, editor.taskData.labels)
    const newDataAnnotationsMap = localFramesDataToRemote(
      editor.framesData,
      editor.taskData.classifications,
    )
    const dataInfos: DataInfo[] = []
    const updatedGeometryIds: string[] = []
    editor.taskData.frames.forEach(({ id }) => {
      const oldObjects = snapshots[id]?.objects || []
      const oldDataAnnotations = snapshots[id]?.dataAnnotations || []
      const newObjects = newObjectsMap[id] || []
      const newDataAnnotations = newDataAnnotationsMap[id] || []
      let hasObjectChanges = false
      if (oldObjects.length !== newObjects.length) {
        hasObjectChanges = true
      }
      newObjects.forEach((newItem) => {
        const oldItem = oldObjects.find((e) => e.frontId === newItem.frontId)
        if (!oldItem) {
          hasObjectChanges = true
          return
        }
        if (!isEqual(newItem, oldItem)) {
          hasObjectChanges = true
          newItem.classAttributes.version = oldItem.classAttributes.version + 1
          updatedGeometryIds.push(newItem.frontId)
        }
      })
      const hasAnnotationChanges = !isEqual(oldDataAnnotations, newDataAnnotations)
      if (hasObjectChanges || hasAnnotationChanges) {
        dataInfos.push({
          dataId: id,
          objects: newObjects,
          dataAnnotations: newDataAnnotations,
        })
      }
    })
    return {
      dataInfos,
      updatedGeometryIds,
    }
  }
  /** 初始加载和每次保存成功后更新 */
  const updateSnapshots = () => {
    const objectsMap = localTrackObjectsToRemote(editor.trackObjects, editor.taskData.labels)
    const dataAnnotationsMap = localFramesDataToRemote(
      editor.framesData,
      editor.taskData.classifications,
    )
    editor.taskData.frames.forEach(({ id }) => {
      snapshots[id] = {
        objects: objectsMap[id] || [],
        dataAnnotations: dataAnnotationsMap[id] || [],
      }
    })
  }

  return {
    getChangedFrames,
    updateSnapshots,
  }
})

export default useSaveDataChangeTracker
