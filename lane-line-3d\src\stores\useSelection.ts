import { createGlobalState, useEventListener } from '@vueuse/core'
import { computed } from 'vue'

import { useEditor } from '@/editors/LaneLine3D/useEditor'

const MULTI_SELECT_KEY = 'Shift'

/**
 * 用于管理选中状态。
 */
const useSelection = createGlobalState(() => {
  const editor = useEditor()
  let isPressing = false
  useEventListener(document, 'keydown', (event) => {
    if (event.key === MULTI_SELECT_KEY) {
      isPressing = true
    }
  })
  useEventListener(document, 'keyup', (event) => {
    if (event.key === MULTI_SELECT_KEY) {
      isPressing = false
    }
  })
  const selectGeometryById = (geometryId: string, enableMultiSelect = true) => {
    editor.applyChanges((trackObjects) => {
      const geometries = trackObjects.map((item) => item.geometries).flat()
      const isMultiSelect = enableMultiSelect && isPressing
      if (!isMultiSelect) {
        geometries.forEach((item) => {
          if (item.selected) {
            item.selected = false
          }
        })
      }
      const target = geometries.find((item) => item.id === geometryId)
      if (!target) return
      if (isMultiSelect) {
        target.selected = !target.selected
      } else {
        target.selected = true
      }
    })
  }
  const selectTrackObjectById = (trackObjectId: string, enableMultiSelect = true) => {
    editor.applyChanges((trackObjects) => {
      const geometries = trackObjects.map((item) => item.geometries).flat()
      const isMultiSelect = enableMultiSelect && isPressing
      if (!isMultiSelect) {
        geometries.forEach((item) => {
          if (item.selected) {
            item.selected = false
          }
        })
      }
      const target = trackObjects.find((item) => item.id === trackObjectId)
      if (!target) return
      if (isMultiSelect) {
        const allSelected = target.geometries.every((item) => item.selected)
        target.geometries.forEach((item) => {
          if (allSelected) {
            item.selected = false
          } else {
            item.selected = true
          }
        })
      } else {
        target.geometries.forEach((item) => {
          item.selected = true
        })
      }
    })
  }
  const selectedGeometries = computed(() => {
    return editor.geometries.filter((item) => item.selected)
  })
  const selectedTrackObjects = computed(() => {
    return editor.trackObjects.filter((item) => item.geometries.some((item) => item.selected))
  })

  const onlySelectedGeometryObject = computed(() => {
    if (selectedGeometries.value.length !== 1) return
    const targetId = selectedGeometries.value[0].id
    return editor.geometryGroup.object.children.find((item) => item.userData.id === targetId)
  })

  return {
    selectGeometryById,
    selectTrackObjectById,
    selectedGeometries,
    selectedTrackObjects,
    onlySelectedGeometryObject,
  }
})

export default useSelection
