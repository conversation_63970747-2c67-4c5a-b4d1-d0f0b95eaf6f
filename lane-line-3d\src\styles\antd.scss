body {
  .ant-tooltip-inner {
    background-color: #333;
  }

  .ant-select,
  .ant-select-arrow,
  .ant-input,
  .ant-radio-wrapper {
    color: #cbcbcb;
  }

  .ant-collapse-item {
    border: none !important;
  }

  .ant-collapse-content {
    color: white;
  }

  .ant-collapse-content-box {
    padding: 0 !important;
    background: #2a2a2c !important;
  }

  .ant-collapse-borderless {
    background-color: transparent;
  }

  .ant-collapse > .ant-collapse-item {
    margin-bottom: 6px;
    background: #1e1f23;

    > .ant-collapse-header {
      padding: 4px;
      padding-left: 40px;
      color: rgb(177 177 177 / 85%);
      text-align: left;

      .ant-collapse-arrow {
        position: absolute;
        top: 50%;
        left: 16px;
        z-index: 2;
        transform: translateY(-50%);
      }
    }
  }

  .ant-dropdown-menu {
    background-color: #3a393e;
  }

  // ant-slider
  .ant-slider-track {
    background-color: #177ddc !important;
  }

  .ant-slider-handle {
    border-color: #177ddc !important;
  }

  .ant-slider-rail {
    background-color: #404040;
  }
}

// 处理由于弹窗 aria-hidden 的报错
.ant-modal div[aria-hidden='true'] {
  display: none !important;
}

.ant-tooltip-inner {
  .tool-extra-tooltip & {
    padding: 4px;
  }
}
