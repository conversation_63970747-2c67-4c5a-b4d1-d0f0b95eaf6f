@import url('./base.css');
@import url('./antd.scss');

html {
  overflow: hidden;
  font-size: 16px;
}

body {
  font-size: 14px;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
}

#app {
  position: relative;
  overflow: hidden;
}

body {
  background-color: #3a393e;
}

.limit {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.border-bottom {
  border-bottom: 1px solid #4a4a4a;
}

.over-not-allowed {
  position: absolute;
  inset: 0;
  cursor: not-allowed;
}

@keyframes loading-360 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading {
  animation: loading-360 0.8s infinite linear;
}

.no-info {
  color: #ccc;
}

.loading-3d-wrap {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;

  .create-status {
    display: block;
    width: 24px;
    height: 23px;
    text-align: center;
  }
}

.anticon > svg {
  vertical-align: baseline;
}
