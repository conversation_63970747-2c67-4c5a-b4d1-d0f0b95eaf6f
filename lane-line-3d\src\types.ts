import type { GeometryTypes } from './const'

export type Optional<T, K extends keyof T> = T extends any
  ? Omit<T, K> & Partial<Pick<T, K>>
  : never

export interface IBaseGeometry<T extends GeometryTypes, D> {
  /** 前端生成的唯一标识符 */
  id: string
  /**
   * 保存到后端后获得的唯一标识符
   */
  backId?: string | number
  /**
   * 关联的帧ID
   */
  frameId: string | number
  /**
   * 几何体类型
   */
  type: T
  /**
   * 储存几何相关数据，如坐标、大小等控制视觉效果的数据
   */
  shape: D
  /**
   * 私有属性的表单值，表单与 classId 和本身的 type 有关
   */
  classData?: Record<string, any>

  /**
   * 【2D标注】相机索引
   * @description
   * 从 0 开始
   * @default -1
   */
  cameraIndex?: number

  /**
   * 版本号
   * @description
   * 首次保存从 1 开始，每次保存使用 isEqual 比较原始数据和最终数据确定是否自增
   */
  version?: number
  /**
   * 创建时间戳
   */
  createAt?: string
  /**
   * 创建者用户ID
   */
  createBy?: number

  /**
   * 【2D标注】是否为投影创建
   * @default false
   */
  isProjection?: boolean
  /**
   * 模型识别的类别
   * @description
   * 如果是模型跑出来的结果会记录识别出来的 class
   */
  modelClass?: string

  /**
   * 标注来源类型
   * @type {"DATA_FLOW" | "MODEL" | "TASK"}
   * @description
   * - "DATA_FLOW": 表示人工标注
   * - "MODEL": 表示模型创建
   * - "TASK": 表示任务导入
   */
  sourceType?: string
  /**
   * 来源ID
   * @description
   * sourceType 为 "MODEL" 时为 model 的 id
   * @default -1
   */
  sourceId?: number
  /**
   * 是否被选中
   */
  selected?: boolean
  /**
   * 是否可见
   */
  visible?: boolean
}

export interface ICameraInternal {
  fx: number
  fy: number
  cx: number
  cy: number
}

export interface IFileConfig {
  dirName: string
  name: string
  url: string
}

export interface IImgViewConfig {
  cameraInternal: { fx: number; fy: number; cx: number; cy: number }
  cameraExternal: number[]
  imgSize: [number, number]
  imgUrl: string
  imgObject: HTMLImageElement
  // rowMajor?: boolean;
  name: string
}
export type PointAttr = 'position' | 'color' | string
export interface IDataResource {
  viewConfig: IImgViewConfig[]
  time: number
  // position
  pointsUrl: string
  pointsData: Record<PointAttr, number[]>
  intensityRange?: [number, number]
  pose?: Record<string, number>
  ground?: number
  name?: string
}
