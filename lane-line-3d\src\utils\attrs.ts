import { cloneDeep } from 'lodash-es'

export enum AttrType {
  RADIO = 'RADIO',
  MULTI_SELECTION = 'MULTI_SELECTION',
  DROPDOWN = 'DROPDOWN',
  TEXT = 'TEXT',
}

export interface IAttr {
  id: string
  key: string
  name: string
  label?: string

  type: AttrType
  required: boolean
  options: { value: any; label: string }[]
  defaultValue: any

  parent: string
  parentAttr: string
  parentValue: any
  leafFlag?: boolean
}

export function normalizeAttrValues(attrs: IAttr[], values: Record<string, any> = {}) {
  const result = {} as Record<string, any>
  attrs.forEach((attr) => {
    result[attr.id] = normalizeAttrValue(attr, values[attr.id])
  })
  return result
}

export function normalizeAttrValue(attr: IAttr, value: any) {
  if (value === undefined || value === null) {
    return attr.type === AttrType.MULTI_SELECTION ? [] : ''
  }
  if (attr.type === AttrType.MULTI_SELECTION && !Array.isArray(value)) {
    return [value]
  }
  return value
}

export function attrsToValues(data: { id: string; value: any }[] = []) {
  const values = {} as Record<string, any>
  data.forEach((v) => {
    values[v.id] = v.value
  })
  return values
}

export function isAttrVisible(
  attr: IAttr,
  attrMap: Record<string, IAttr>,
  formData: Record<string, any>,
): boolean {
  if (!attr.parent) return true
  const parentAttr = attrMap[attr.parent]
  const parentValue = formData[parentAttr.id]
  const visible =
    parentAttr.type !== AttrType.MULTI_SELECTION
      ? parentValue === attr.parentValue
      : parentValue && (parentValue as any[]).indexOf(attr.parentValue) >= 0
  return visible && isAttrVisible(parentAttr, attrMap, formData)
}

export function isAttrValueNonNullable(attr: IAttr, value: any) {
  if (attr.type === AttrType.MULTI_SELECTION) {
    return Array.isArray(value) && value.length > 0
  } else {
    return !!value
  }
}

export function valuesToAttrs(formData: Record<string, any> = {}, attrs: IAttr[] = []) {
  formData = normalizeAttrValues(attrs, formData)
  const attrMap = {} as Record<string, IAttr>
  attrs = cloneDeep(attrs)
  attrs.forEach((attr) => {
    attrMap[attr.id] = attr
  })
  attrs = attrs.filter(
    (e) => isAttrVisible(e, attrMap, formData) && isAttrValueNonNullable(e, formData[e.id]),
  )
  attrs.forEach((e) => {
    e.leafFlag = true
  })
  attrs.forEach((e) => {
    const parent = e.parent && attrMap[e.parent] ? attrMap[e.parent] : null
    if (parent) parent.leafFlag = false
  })

  const data = attrs.map((e) => {
    const isParentMulti = e.parent && attrMap[e.parent]?.type === AttrType.MULTI_SELECTION
    return {
      id: e.id,
      pid: e.parent ? e.parent : null,
      name: e.name,
      value: formData[e.id],
      type: e.type,
      pvalue: isParentMulti ? e.parentValue : undefined,
      alias: e.label!,
      isLeaf: !!e.leafFlag,
    }
  })
  return data
}
