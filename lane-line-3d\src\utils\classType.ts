import { cloneDeep } from 'lodash-es'
import * as THREE from 'three'

import type { Attribute, ClassTypeDefinition } from '@/api/typings/common'
import { GeometryTypes } from '@/const'

import { AttrType, type IAttr } from './attrs'

export interface IClassTypeAttr extends IAttr {
  classId: string | number
  /**
   * 来源于哪个值，目前有 'attributes' | '2d_attributes' | '3d_attributes'
   */
  from: keyof ClassTypeDefinition
}

export interface IClassType {
  id: string | number
  label: string
  name: string
  identification: string
  color: string
  type?: '' | 'constraint' | 'standard'
  size3D?: THREE.Vector3
  sizeMin?: THREE.Vector3
  sizeMax?: THREE.Vector3
  points?: [number, number]
  attrs: IClassTypeAttr[]
}

type PrivateKeys = '3d_attributes' | '2d_attributes'
export const COMMON_KEY: keyof ClassTypeDefinition = 'attributes'
export const PRIVATE_KEY_MAP: { [key in PrivateKeys]: GeometryTypes[] } = {
  '3d_attributes': [
    GeometryTypes.Polyline3D,
    GeometryTypes.Polygon3D,
    GeometryTypes.Point3D,
    GeometryTypes.Box3D,
  ],
  '2d_attributes': [GeometryTypes.MultiRect2D],
}
const ALL_ATTR_KEYS = [COMMON_KEY, ...Object.keys(PRIVATE_KEY_MAP)] as (keyof ClassTypeDefinition)[]

function getPrivateKey(objType: GeometryTypes) {
  for (const [key, values] of Object.entries(PRIVATE_KEY_MAP)) {
    if (values.includes(objType)) {
      return key as keyof ClassTypeDefinition
    }
  }
  return undefined
}

export function getAllClassAttrs(classType: IClassType | undefined, objType: GeometryTypes) {
  if (!classType) {
    return []
  }
  const sources = [COMMON_KEY]
  const privateKey = getPrivateKey(objType)
  if (privateKey) {
    sources.push(privateKey)
  }
  return cloneDeep(classType.attrs).filter((attr) => sources.includes(attr.from))
}
export function getCommonClassAttrs(classType: IClassType | undefined) {
  return cloneDeep(classType?.attrs || []).filter((attr) => attr.from === COMMON_KEY)
}
export function getPrivateClassAttrs(classType: IClassType | undefined, objType: GeometryTypes) {
  const privateKey = getPrivateKey(objType)
  if (!privateKey) {
    return []
  }
  return cloneDeep(classType?.attrs || []).filter((attr) => attr.from === privateKey)
}

export function filterAttrValuesBySource(
  classType: IClassType | undefined,
  allValues: Record<string, any> = {},
  source?: string,
) {
  const values: Record<string, any> = {}
  if (!classType || !source) {
    return values
  }
  classType.attrs
    .filter((attr) => attr.from === source)
    .forEach((attr) => {
      if (attr.id in allValues) {
        values[attr.id] = allValues[attr.id]
      }
    })
  return values
}

export function filterCommonClassData(
  classType: IClassType | undefined,
  allValues: Record<string, any> = {},
) {
  return filterAttrValuesBySource(classType, allValues, COMMON_KEY)
}

export function filterPrivateClassData(
  classType: IClassType | undefined,
  allValues: Record<string, any> = {},
  objType: GeometryTypes,
) {
  return filterAttrValuesBySource(classType, allValues, getPrivateKey(objType))
}

function getDefaultAttrValuesBySources(classType: IClassType | undefined, sources: string[]) {
  const values: Record<string, any> = {}
  if (!classType) {
    return values
  }
  classType.attrs
    .filter((attr) => sources.includes(attr.from))
    .forEach((attr) => {
      const defaultValue = attr.defaultValue?.name
      if (defaultValue) {
        values[attr.id] = defaultValue
      }
    })
  return values
}

export function getDefaultCommonClassData(classType: IClassType | undefined) {
  return getDefaultAttrValuesBySources(classType, [COMMON_KEY])
}
export function getDefaultPrivateClassData(
  classType: IClassType | undefined,
  objType: GeometryTypes,
) {
  const privateKey = getPrivateKey(objType)
  if (!privateKey) {
    return {}
  }
  return getDefaultAttrValuesBySources(classType, [privateKey])
}

export function convertClassDefinitions(data: ClassTypeDefinition[]) {
  return data.map((config) => {
    const classType: IClassType = {
      id: config.id || config.name,
      name: config.name || '',
      label: config.name || '',
      color: config.color || '#ff0000',
      attrs: [],
      type: '',
      identification: config.identification,
    }

    processToolOptions(classType, config.toolTypeOptions)

    ALL_ATTR_KEYS.forEach((key) => {
      parseAttributesRecursively(classType.attrs, config[key], {
        classId: classType.id,
        from: key,
        parent: '',
        parentAttr: config.name,
        parentValue: '',
      })
    })

    return classType
  })
}

function parseAttributesRecursively(
  result: IClassTypeAttr[],
  rawAttrs: Attribute[] = [],
  payload: Pick<IClassTypeAttr, 'classId' | 'from' | 'parent' | 'parentAttr' | 'parentValue'>,
) {
  rawAttrs.forEach((rawAttr) => {
    const name = rawAttr.name
    const { parent, parentValue } = payload
    const classAttr: IClassTypeAttr = {
      ...payload,

      id: rawAttr.id || name,
      key: parent ? `${parent}[${parentValue}]-${name}` : name,
      name,
      label: name,

      type: rawAttr.type as AttrType,
      required: rawAttr.required,
      options: (rawAttr.options || []).map((e) => {
        return { value: e.name, label: e.name }
      }),
      defaultValue: rawAttr.options.find((e) => e.default),
    }

    result.push(classAttr)

    rawAttr.options?.forEach((option) => {
      parseAttributesRecursively(result, option.attributes, {
        ...payload,
        parent: classAttr.id,
        parentAttr: name,
        parentValue: option.name,
      })
    })
  })

  return result
}

function processToolOptions(classType: IClassType, toolOption: any = {}) {
  if (toolOption.isStandard) {
    classType.type = 'standard'
    classType.size3D = new THREE.Vector3(
      toolOption.length || 0,
      toolOption.width || 0,
      toolOption.height || 0,
    )
  } else if (toolOption.isConstraints) {
    const length = toolOption.length || []
    const width = toolOption.width || []
    const height = toolOption.height || []
    classType.type = 'constraint'
    classType.sizeMin = new THREE.Vector3(length[0] || 0, width[0] || 0, height[0] || 0)
    classType.sizeMax = new THREE.Vector3(length[1] || 0, width[1] || 0, height[1] || 0)
  }

  if (toolOption.points) {
    classType.points = [toolOption.points, 0]
  }
}
