import type { DataAnnotation } from '@/api/typings/annotation'
import type { ClassificationDefinition } from '@/api/typings/common'
import type { IFrameData } from '@/editors/LaneLine3D/useEditor'

import { AttrType, type IAttr, valuesToAttrs } from './attrs'

export interface IClassification {
  id: number
  name: string
  label?: string
  attrs: IClassificationAttr[]
}

export interface IClassificationAttr extends IAttr {
  classificationId: number
}

export function convertClassificationDefinitions(data: ClassificationDefinition[]) {
  return data.map((scene) => {
    const rawAttrs = scene.attributes || []
    const result: IClassification = {
      id: scene.id,
      name: scene.name,
      label: scene.name,
      attrs: rawAttrs.map((item) => {
        return {
          id: item.id,
          key: item.name,
          classificationId: scene.id,
          parent: '',
          parentValue: '',
          parentAttr: scene.name,
          type: item.type as AttrType,
          name: item.name,
          label: item.name,
          value: item.type === AttrType.MULTI_SELECTION ? [] : '',
          required: item.required,
          defaultValue: item.options.find((e) => e.default),
          options: item.options.map((e) => {
            return { value: e.name, label: e.name }
          }),
        }
      }),
    }
    return result
  })
}

export function localClassificationDataToRemote(
  classificationData: Record<string, any>,
  classifications: IClassification[],
) {
  return classifications.map((item) => {
    return {
      classificationId: item.id,
      classificationAttributes: {
        id: item.id,
        values: valuesToAttrs(classificationData, item.attrs),
      },
    }
  })
}

export function localFramesDataToRemote(
  framesData: IFrameData[],
  classifications: IClassification[],
) {
  return framesData.reduce(
    (o, item) => {
      Object.assign(o, {
        [item.frameId]: localClassificationDataToRemote(item.classificationData, classifications),
      })
      return o
    },
    {} as Record<string, DataAnnotation[]>,
  )
}
