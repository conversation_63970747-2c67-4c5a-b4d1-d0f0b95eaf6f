import * as THREE from 'three'

import type { IFileConfig, IImgViewConfig } from '@/types'

export function isMatrixColumnMajor(elements: number[]) {
  const rightZero = elements[3] === 0 && elements[7] === 0 && elements[11] === 0
  const bottomHasOne = !!elements[12] || !!elements[13] || !!elements[14]
  return rightZero && bottomHasOne
}

export interface ICameraConfig {
  cameraExternal?: number[]
  cameraInternal?: { cx: number; cy: number; fx: number; fy: number }
  rowMajor?: boolean
  camera_external?: number[]
  camera_internal?: { cx: number; cy: number; fx: number; fy: number }
}

export function translateCameraConfig(info?: ICameraConfig) {
  if (!info) return null
  let cameraExternal = info.cameraExternal || info.camera_external
  const cameraInternal = info.cameraInternal || info.camera_internal

  if (!cameraExternal || cameraExternal.length !== 16) return null

  // to rowMajor
  if (info.rowMajor === false || isMatrixColumnMajor(cameraExternal)) {
    const matrix = new THREE.Matrix4()
    matrix.elements = cameraExternal
    matrix.transpose()
    cameraExternal = matrix.elements
  }

  return { cameraExternal, cameraInternal }
}

export function clamRange(v: number, min: number, max: number) {
  return Math.max(Math.min(max, v), min)
}

export function createViewConfig(fileConfig: IFileConfig[], cameraInfos: any[]) {
  let viewConfig = [] as IImgViewConfig[]
  let pointsUrl = ''
  const regLidar = new RegExp(/point(_?)cloud/i)
  const regImage = new RegExp(/image/i)
  fileConfig.forEach((e) => {
    if (regLidar.test(e.dirName)) {
      pointsUrl = e.url
    } else if (regImage.test(e.dirName)) {
      const index = +(e.dirName.match(/[0-9]{1,5}$/) as any)[0]
      viewConfig[index] = {
        cameraInternal: { fx: 0, fy: 0, cx: 0, cy: 0 },
        cameraExternal: [],
        imgSize: [0, 0],
        imgUrl: e.url,
        name: e.name,
        imgObject: null as any,
      }
    }
  })
  viewConfig = viewConfig.filter((e) => !!e)
  viewConfig.forEach((config, index) => {
    const info = cameraInfos[index]

    const translateInfo = translateCameraConfig(info)
    if (!translateInfo) return

    config.cameraExternal = translateInfo.cameraExternal
    config.cameraInternal = translateInfo.cameraInternal!
    config.imgSize = [info.width, info.height]
    // config.rowMajor = info.rowMajor;
  })

  // filter
  viewConfig = viewConfig.filter((e) => e.cameraExternal.length === 16 && e.cameraInternal)

  return { pointsUrl, config: viewConfig }
}

export function rand(start: number, end: number) {
  return (Math.random() * (end - start) + start) | 0
}

export function empty(value: any) {
  return value === null || value === undefined || value === ''
}

export function queryStr(data: Record<string, any> = {}) {
  const queryArr = [] as string[]
  Object.keys(data).forEach((name) => {
    const value = data[name]
    if (Array.isArray(value)) {
      queryArr.push(`${name}=${value.join(',')}`)
    } else {
      queryArr.push(`${name}=${value}`)
    }
  })

  return queryArr.join('&')
}

export function formatNumDot(str: string | number, precision: number = 2): string {
  str = '' + str
  const regex = /(?!^)(?=(\d{3})+(\.|$))/g
  str.replace(regex, ',')

  if (precision) {
    return (+str).toFixed(precision)
  } else {
    return str
  }
}

export function formatNumStr(str: string | number, precision: number = 2): string {
  str = '' + str
  if (precision) {
    return (+str).toFixed(precision)
  } else {
    return str
  }
}
