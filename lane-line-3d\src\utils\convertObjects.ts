import dayjs from 'dayjs'

import type { AnnotatedObject, Contour, ObjectItem } from '@/api/typings/annotation'
import { GeometryTypes } from '@/const'
import { box3DConverter } from '@/objects/Box3D/setup'
import { multiRect2DConverter } from '@/objects/MutiRect2D/setup'
import { point3DConverter } from '@/objects/Point3D/setup'
import { polygon3DConverter } from '@/objects/Polygon3D/setup'
import { polyline3DConverter } from '@/objects/Polyline3D/setup'
import { attrsToValues, valuesToAttrs } from '@/utils/attrs'
import {
  filterCommonClassData,
  filterPrivateClassData,
  getAllClassAttrs,
  type IClassType,
} from '@/utils/classType'

import type { IGeometry, ITrackObject } from '../editors/LaneLine3D/useEditor'

type GeometryTypeMap = {
  [K in IGeometry as K['type']]: K
}

type Converter<T extends keyof GeometryTypeMap> = {
  toRemote: (shape: GeometryTypeMap[T]['shape']) => Contour
  toLocal: (contour: Contour) => GeometryTypeMap[T]['shape']
}

const converters: {
  [T in keyof GeometryTypeMap]: Converter<T>
} = {
  [GeometryTypes.Point3D]: point3DConverter,
  [GeometryTypes.Polyline3D]: polyline3DConverter,
  [GeometryTypes.Polygon3D]: polygon3DConverter,
  [GeometryTypes.Box3D]: box3DConverter,
  [GeometryTypes.MultiRect2D]: multiRect2DConverter,
}

function getConverter<T extends keyof GeometryTypeMap>(type: T): Converter<T> | undefined {
  return converters[type]
}

export function localTrackObjectsToRemote(trackObjects: ITrackObject[], labels: IClassType[]) {
  const results: Record<string, ObjectItem[]> = {}
  trackObjects.forEach((track) => {
    track.geometries.forEach((geo) => {
      const share = {
        frontId: geo.id,
        classId: track.classId,
        sourceId: geo.sourceId || -1,
        sourceType: geo.sourceType || 'DATA_FLOW',
      }
      results[geo.frameId] ||= []
      results[geo.frameId].push({
        ...share,
        source: 'ARTIFICIAL',
        classAttributes: {
          ...share,
          id: geo.id,
          type: geo.type,

          trackId: track.id,
          trackName: track.name || '',

          modelClass: '',
          className: '',

          contour: getConverter(geo.type)?.toRemote(geo.shape) ?? {},

          classValues: valuesToAttrs(
            Object.assign({}, track.classData, geo.classData),
            getAllClassAttrs(labels.find((e) => e.id === track.classId)!, geo.type),
          ),

          meta: {
            isProjection: false,
          },
          version: geo.version || 1,
          createdAt: dayjs(geo.createAt || Date.now())
            .utc()
            .format(),
          createdBy: geo.createBy || 1,
        },
        remark: '',
      })
    })
  })
  return results
}

export function remoteToLocalTrackObjects(objects: AnnotatedObject[], labels: IClassType[]) {
  const trackObjects: ITrackObject[] = []
  objects.forEach((item) => {
    let trackObject = trackObjects.find((track) => track.id === item.classAttributes.trackId)
    const label = labels.find((e) => e.id === item.classAttributes.classId)
    const allClassData = attrsToValues(item.classAttributes.classValues)
    if (!trackObject) {
      trackObject = {
        id: item.classAttributes.trackId,
        name: item.classAttributes.trackName,
        classId: item.classAttributes.classId,
        classData: filterCommonClassData(label, allClassData),
        geometries: [],
      }
      trackObjects.push(trackObject)
    }
    const type = item.classAttributes.type as keyof GeometryTypeMap
    trackObject.geometries.push({
      id: item.classAttributes.frontId,
      backId: item.id,
      frameId: item.dataId,
      type,
      shape: getConverter(type)?.toLocal(item.classAttributes.contour) ?? ({} as any),
      version: item.classAttributes.version,
      createAt: item.classAttributes.createdAt,
      createBy: item.classAttributes.createdBy,
      sourceId: item.sourceId,
      sourceType: item.sourceType,
      classData: filterPrivateClassData(label, allClassData, type),
    })
  })
  return trackObjects
}
