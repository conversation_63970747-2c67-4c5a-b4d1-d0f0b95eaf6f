import * as THREE from 'three'

import type { ICameraInternal } from '@/types'

const raycaster = new THREE.Raycaster()

export function getNDCFromDOMPoint(
  domElement: HTMLElement,
  point: { x: number; y: number },
  target?: THREE.Vector2,
) {
  const { clientHeight: height, clientWidth: width } = domElement
  const x = (point.x / width) * 2 - 1
  const y = (-point.y / height) * 2 + 1
  target = target || new THREE.Vector2()
  target.set(x, y)
  return target
}

/**
 * 获取 canvas 坐标系下的点在 object 物体上的世界坐标
 */
export function canvasToWorld(
  domElement: HTMLElement,
  camera: THREE.Camera,
  point: THREE.Vector2,
  object: THREE.Object3D,
) {
  const coords = getNDCFromDOMPoint(domElement, point)
  raycaster.setFromCamera(coords, camera)

  const intersects = raycaster.intersectObject(object)
  if (intersects.length) {
    const point = intersects[0].point
    return point
  }
  return new THREE.Vector3()
}

export function worldToCanvas(
  domElement: HTMLElement,
  camera: THREE.Camera,
  worldPosition: THREE.Vector3,
): THREE.Vector3 {
  const target = new THREE.Vector3()
  target.copy(worldPosition)
  target.applyMatrix4(camera.matrixWorldInverse)
  if (target.z >= 0) {
    target.z = -0.0001
  }

  target.applyMatrix4(camera.projectionMatrix)
  const { clientHeight: height, clientWidth: width } = domElement

  target.x = ((target.x + 1) / 2) * width
  target.y = (-(target.y - 1) / 2) * height
  return target
}

export function createMatrixFromCameraInternal(
  option: ICameraInternal,
  w: number,
  h: number,
): THREE.Matrix4 {
  const NEAR = 0.01
  const FAR = 10000
  const { fx, fy, cy, cx } = option
  // return new THREE.Matrix4().set(
  //     2*fx / w,       0,                   1 - 2*cx / w,                 0,
  //     0,           2*fy / h,          2*cy / h - 1,                      0,
  //     0,             0,            (near + far) / (near - far),   2*far*near / (near - far),
  //     0,             0,                      -1.0,                       0);

  return new THREE.Matrix4().set(
    (2 * fx) / w,
    0,
    1 - (2 * cx) / w,
    0,
    0,
    (2 * fy) / h,
    (2 * cy) / h - 1,
    0,
    0,
    0,
    (NEAR + FAR) / (NEAR - FAR),
    (2 * FAR * NEAR) / (NEAR - FAR),
    0,
    0,
    -1,
    0,
  )
}

export function isPointInRect(pos: THREE.Vector2, rect: THREE.Vector2[]) {
  const A = rect[0]
  const B = rect[1]
  const C = rect[2]
  const D = rect[3]
  const a = (B.x - A.x) * (pos.y - A.y) - (B.y - A.y) * (pos.x - A.x)
  const b = (C.x - B.x) * (pos.y - B.y) - (C.y - B.y) * (pos.x - B.x)
  const c = (D.x - C.x) * (pos.y - C.y) - (D.y - C.y) * (pos.x - C.x)
  const d = (A.x - D.x) * (pos.y - D.y) - (A.y - D.y) * (pos.x - D.x)
  if ((a > 0 && b > 0 && c > 0 && d > 0) || (a < 0 && b < 0 && c < 0 && d < 0)) {
    return true
  }
  return false
}

export function getMaxMinV2(positions: THREE.Vector2[]) {
  let minX = Infinity
  let maxX = -Infinity
  let minY = Infinity
  let maxY = -Infinity
  // let minZ = Infinity;
  // let maxZ = -Infinity;
  positions.forEach((pos) => {
    if (pos.x < minX) minX = pos.x
    if (pos.x > maxX) maxX = pos.x
    if (pos.y < minY) minY = pos.y
    if (pos.y > maxY) maxY = pos.y
    // if (pos.z < minZ) minZ = pos.z;
    // if (pos.z > maxZ) maxZ = pos.z;
  })
  return { minX, maxX, minY, maxY }
}

const line = new THREE.Line3()
const intersect = new THREE.Vector3()
const dir = new THREE.Vector3()
const frustum = new THREE.Frustum()
export function reformProjectPoints(
  positionsFrontV3: THREE.Vector3[],
  positionsBackV3: THREE.Vector3[],
  camera: THREE.PerspectiveCamera,
) {
  frustum.setFromProjectionMatrix(
    camera.projectionMatrix.clone().multiply(camera.matrixWorldInverse),
  )
  // let n = 0;

  // console.log(JSON.parse(JSON.stringify([...positionsFrontV3, ...positionsBackV3])));

  // console.log('frustum.planes[0]', frustum.planes[0].normal);
  traverseLine(positionsFrontV3, positionsBackV3, (p1, p2) => {
    // if (intersectHandle(p1, p2, frustum.planes[0])) n++;
    intersectHandle(p1, p2, frustum.planes[0])
  })

  traverseLine(positionsFrontV3, positionsBackV3, (p1, p2) => {
    intersectHandle(p1, p2, frustum.planes[1])
    // if (intersectHandle(p1, p2, frustum.planes[1])) n++;
  })

  // console.log({ n });
}
function traverseLine(
  positionsFrontV3: THREE.Vector3[],
  positionsBackV3: THREE.Vector3[],
  callback: (p1: THREE.Vector3, p2: THREE.Vector3) => void,
) {
  positionsFrontV3.forEach((front, index) => {
    const back = positionsBackV3[index]
    callback(front, back)
  })

  positionsFrontV3.forEach((p1, index) => {
    const p2 = positionsFrontV3[(index + 1) % 4]
    callback(p1, p2)
  })

  positionsBackV3.forEach((p1, index) => {
    const p2 = positionsBackV3[(index + 1) % 4]
    callback(p1, p2)
  })
}

function intersectHandle(start: THREE.Vector3, end: THREE.Vector3, plane: THREE.Plane) {
  // 同一个平面不处理
  const dotV = dir.copy(end).sub(start).dot(plane.normal)

  // console.log(JSON.parse(JSON.stringify(start)), JSON.parse(JSON.stringify(end)));
  // console.log('dotV:', dotV);
  if (Math.abs(dotV) < 0.000001) return

  line.start.copy(start)
  line.end.copy(end)

  const flag = plane.intersectLine(line, intersect)
  // console.log('flag', flag);
  if (flag) {
    dir.copy(start).sub(intersect)
    if (dir.dot(plane.normal) < 0) {
      start.copy(intersect)
    }

    dir.copy(end).sub(intersect)
    if (dir.dot(plane.normal) < 0) {
      end.copy(intersect)
    }
  }

  return flag
}

const temp_1 = new THREE.Vector2()
const temp_2 = new THREE.Vector2()

export function drawRect(
  context: CanvasRenderingContext2D,
  center: THREE.Vector2,
  size: THREE.Vector2,
) {
  temp_1.copy(size).multiplyScalar(0.5)
  temp_2.copy(center).sub(temp_1)
  context.strokeRect(temp_2.x, temp_2.y, size.x, size.y)
}

export function drawText(
  ctx: CanvasRenderingContext2D,
  center: THREE.Vector2,
  size: THREE.Vector2,
  text: string,
) {
  ctx.save()
  const trans = ctx.getTransform()
  const transform = (pos: THREE.Vector2) => {
    return pos
      .clone()
      .multiply(new THREE.Vector2(trans.a, trans.d))
      .add(new THREE.Vector2(trans.e, trans.f))
  }
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  const fontSize = 12
  const padding = new THREE.Vector2(4, 3)
  const offset = new THREE.Vector2(5, -5)
  ctx.font = `${fontSize}px Arial`
  /** 左上角顶点 */
  const tlPos = transform(new THREE.Vector2(center.x - size.x / 2, center.y - size.y / 2))
  const textMetrics = ctx.measureText(text)
  ctx.fillStyle = 'rgba(0,0,0,0.7)'
  const lineHeight = fontSize + padding.y * 2
  ctx.fillRect(
    tlPos.x + offset.x,
    tlPos.y - lineHeight + offset.y,
    textMetrics.width + padding.x * 2,
    lineHeight,
  )
  ctx.fillStyle = '#fff'
  ctx.fillText(text, tlPos.x + padding.x + offset.x, tlPos.y - padding.y + offset.y)
  ctx.restore()
}
