import * as THREE from 'three'

import { ViewEvent } from '@/const'

export default class BaseViewController extends THREE.EventDispatcher {
  id: string
  renderId: string = ''
  name: string = ''
  enabled: boolean = true
  userData: Record<string, any> = {}

  private renderTimer: number = 0

  constructor(name?: string) {
    super()

    if (name) this.name = name
    this.id = THREE.MathUtils.generateUUID()
  }

  /**
   * @param enabled
   */
  toggle(enabled: boolean) {
    this.enabled = enabled

    if (enabled) this.render()
  }

  isEnable(): boolean {
    return this.enabled
  }

  private _render() {
    this.dispatchEvent({ type: ViewEvent.RENDER_BEFORE })
    this.renderFrame()
    this.renderTimer = 0
    this.dispatchEvent({ type: ViewEvent.RENDER_AFTER })
  }

  renderFrame() {
    throw new Error('must implement method renderFrame;')
  }

  render(immediate = false) {
    if (!this.isEnable()) return
    if (immediate) {
      this._render()
      return
    }
    if (this.renderTimer) return
    this.renderTimer = requestAnimationFrame(this._render.bind(this))
  }

  addSubRender(callback: () => void) {
    this.addEventListener(ViewEvent.RENDER_AFTER, callback)
  }
  removeSubRender(callback: () => void) {
    this.removeEventListener(ViewEvent.RENDER_AFTER, callback)
  }
}
