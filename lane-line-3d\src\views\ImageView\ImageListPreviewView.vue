<script setup lang="ts">
import { useAsyncState } from '@vueuse/core'
import * as THREE from 'three'
import { computed, watch } from 'vue'

import { fetchResource } from '@/api/common'
import { type ICameraConfig, translateCameraConfig } from '@/utils/common'

import ImageViewPane from './ImageViewPane.vue'
import ImageViewRenderer from './ImageViewRenderer.vue'

const props = defineProps<{
  configUrl: string
  imageUrls: string[]
  pose?: {
    x: number
    y: number
    z: number
    w: number
    pose_x: number
    pose_y: number
    pose_z: number
  }
}>()
defineEmits(['dblclick-item'])

const { state: cameraConfigs, execute } = useAsyncState(async () => {
  if (!props.configUrl) {
    return []
  }
  const data = (await fetchResource(props.configUrl)) as ICameraConfig[]
  return data.map((e) => translateCameraConfig(e)!)
}, [])

watch(
  () => props.configUrl,
  () => {
    execute()
  },
)

function setPose(
  cameraExternal: number[],
  pose?: {
    x: number
    y: number
    z: number
    w: number
    pose_x: number
    pose_y: number
    pose_z: number
  },
) {
  if (!pose) {
    return cameraExternal
  }

  // 从位姿数据创建矩阵
  // 解析四元数和位置信息
  const position = new THREE.Vector3(pose.pose_x || 0, pose.pose_y || 0, pose.pose_z || 0)

  const quaternion = new THREE.Quaternion(pose.x || 0, pose.y || 0, pose.z || 0, pose.w || 1)

  const scale = new THREE.Vector3(1, 1, 1)
  const poseMatrix = new THREE.Matrix4()

  poseMatrix.compose(position, quaternion, scale)
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  const cameraExternalMatrix = new THREE.Matrix4().set(...cameraExternal)
  cameraExternalMatrix.multiply(poseMatrix.invert())
  // 还原成行优先
  return cameraExternalMatrix.transpose().toArray()
}

const items = computed(() => {
  return cameraConfigs.value.map((item, index) => {
    return {
      cameraExternal: setPose(item.cameraExternal, props.pose),
      cameraInternal: item.cameraInternal!,
      imgUrl: props.imageUrls[index],
    }
  })
})
</script>

<template>
  <ImageViewRenderer class="relative bg-[#1e1f23]">
    <template #default="{ controller }">
      <div
        class="absolute z-10 flex h-full w-full flex-col gap-1 overflow-auto p-1"
        @scroll="controller.render()"
      >
        <div
          v-for="(item, index) in items"
          :key="index"
          class="relative w-full"
          @dblclick="$emit('dblclick-item', { ...item, viewIndex: index })"
        >
          <ImageViewPane
            v-bind="item"
            class="w-full"
            :style="{
              'aspect-ratio': '1.78 / 1',
            }"
            :view-index="index"
          />
        </div>
      </div>
    </template>
  </ImageViewRenderer>
</template>
