import * as THREE from 'three'

export interface IImageView {
  isEnable(): boolean
  renderFrame(): void
}

/**
 * 图像渲染管理器 - 负责管理多个图像视图的共享渲染资源
 */
export default class ImageRenderManager {
  container: HTMLElement | null = null
  views: IImageView[] = []

  canvas: HTMLCanvasElement
  context: CanvasRenderingContext2D

  renderer: THREE.WebGLRenderer

  width = 0
  height = 0
  clientRect: DOMRect = {} as DOMRect

  constructor() {
    const canvas = document.createElement('canvas')
    canvas.className = 'render-2d-manager'
    Object.assign(canvas.style, {
      position: 'absolute',
      inset: '0px',
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
    })
    this.canvas = canvas
    this.context = canvas.getContext('2d')!

    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    this.renderer.sortObjects = false
    this.renderer.autoClear = false
    this.renderer.setClearColor(new THREE.Color(0, 0, 0), 0)
    Object.assign(this.renderer.domElement.style, {
      position: 'absolute',
      inset: '0px',
      width: '100%',
      height: '100%',
    })
  }

  /**
   * 添加视图到渲染管理器
   */
  addView = (view: IImageView) => {
    if (this.views.indexOf(view) >= 0) return
    this.views.push(view)
  }

  /**
   * 从渲染管理器移除视图
   */
  removeView = (view: IImageView) => {
    const index = this.views.indexOf(view)
    if (index >= 0) {
      this.views.splice(index, 1)
    }
  }

  /**
   * 检查是否需要渲染
   */
  needRender = () => {
    return this.views.filter((view) => view.isEnable()).length > 0
  }

  /**
   * 更新画布尺寸
   */
  updateSize = () => {
    if (!this.container) return
    this.clientRect = this.container.getBoundingClientRect()

    const width = this.clientRect.width || 100
    const height = this.clientRect.height || 100

    if (width !== this.width || height !== this.height) {
      this.width = width
      this.height = height

      this.canvas.width = width
      this.canvas.height = height

      this.renderer.setSize(width, height, false)
    }
  }

  /**
   * 清除画布内容
   */
  clear = () => {
    const { context, renderer } = this

    context.clearRect(0, 0, this.width, this.height)

    renderer.setScissorTest(false)
    renderer.clear()
    renderer.setScissorTest(true)
  }

  /**
   * 渲染所有视图
   */
  renderAll = () => {
    if (!this.needRender()) return

    this.updateSize()
    this.clear()

    const { context } = this
    this.views.forEach((view) => {
      if (view.isEnable()) {
        context.save()
        view.renderFrame()
        context.restore()
      }
    })
  }

  /**
   * 获取客户端矩形区域
   */
  getClientRect = () => {
    return this.clientRect
  }

  /**
   * 挂载到容器
   */
  mount = (container: HTMLElement) => {
    this.unmount()
    this.container = container
    container.appendChild(this.canvas)
    container.appendChild(this.renderer.domElement)
  }

  /**
   * 从容器卸载
   */
  unmount = () => {
    if (this.container) {
      this.container.removeChild(this.canvas)
      this.container.removeChild(this.renderer.domElement)
      this.container = null
    }
  }
}
