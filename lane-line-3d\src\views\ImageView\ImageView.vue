<script setup lang="ts">
import type { ICameraInternal } from '@/types'

import ImageViewPane from '../ImageView/ImageViewPane.vue'
import ImageViewRenderer from '../ImageView/ImageViewRenderer.vue'

const props = defineProps<{
  viewIndex: number
  cameraInternal: ICameraInternal
  cameraExternal: Array<number>
  imgUrl: string
}>()
</script>

<template>
  <ImageViewRenderer class="relative h-full w-full">
    <ImageViewPane
      v-bind="props"
      class="relative h-full w-full"
      interactive
      :view-index="viewIndex"
    />
  </ImageViewRenderer>
</template>
