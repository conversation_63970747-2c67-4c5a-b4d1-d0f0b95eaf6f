import * as THREE from 'three'

import { ViewEvent } from '@/const'
import { type ICameraInternal } from '@/types'
import { get } from '@/utils'

import BaseViewController from '../BaseViewController'
import ImageRenderManager, { type IImageView } from './ImageRenderManager'

export interface IOption {
  cameraInternal: ICameraInternal
  cameraExternal: Array<number>
  imgSize?: [number, number]
  imgObject: HTMLImageElement
}

export default class ImageViewController extends BaseViewController implements IImageView {
  container: HTMLDivElement

  proxyTransformMatrix: THREE.Matrix4 = new THREE.Matrix4()
  // local matrix
  containerMatrix: THREE.Matrix4 = new THREE.Matrix4()
  fitMatrix: THREE.Matrix4 = new THREE.Matrix4()
  transformMatrix: THREE.Matrix4 = new THREE.Matrix4()

  width: number
  height: number
  renderManager: ImageRenderManager
  clientRect: DOMRect = {} as DOMRect
  option: IOption | null = null
  // img
  imgEl: HTMLImageElement | null = null
  imgSize: THREE.Vector2 = new THREE.Vector2(1, 1)

  constructor(container: HTMLDivElement, renderManager: ImageRenderManager) {
    super()

    this.container = container

    this.width = this.container.clientWidth || 10
    this.height = this.container.clientHeight || 10

    this.renderManager = renderManager
    this.renderManager.addView(this)
  }

  updateAspectRatioConfig = () => {
    const { imgSize, width, height, imgEl: img } = this

    if (!img) return

    const scaleX = imgSize.x / width
    const scaleY = imgSize.y / height

    let scale = 1
    let offsetX = 0
    let offsetY = 0
    if (scaleX > scaleY) {
      scale = 1 / scaleX
      offsetY = (height - imgSize.y * scale) / 2
    } else {
      scale = 1 / scaleY
      offsetX = (width - imgSize.x * scale) / 2
    }
    this.fitMatrix.makeScale(scale, scale, 1)

    const translate = get(THREE.Matrix4)
    translate.makeTranslation(offsetX, offsetY, 0)
    this.fitMatrix.premultiply(translate)
  }

  setOptions = (option: IOption) => {
    this.option = option
    const { imgObject } = option

    this.imgEl = imgObject

    this.imgSize.set(imgObject.naturalWidth, imgObject.naturalHeight)
    this.updateAspectRatioConfig()
    this.dispatchEvent({ type: ViewEvent.OPTIONS_CHANGE, data: option })
    this.render()
  }

  imgToDom = (imgPos: THREE.Vector2 | THREE.Vector3) => {
    const pos = get(THREE.Vector3, 0)
    pos.set(imgPos.x, imgPos.y, 0).applyMatrix4(this.transformMatrix)
    imgPos.x = pos.x
    imgPos.y = pos.y
  }

  domToImg = (imgPos: THREE.Vector2 | THREE.Vector3) => {
    const pos = get(THREE.Vector3, 0)
    const invertMatrix = get(THREE.Matrix4, 0).copy(this.transformMatrix).invert()
    pos.set(imgPos.x, imgPos.y, 0).applyMatrix4(invertMatrix)
    imgPos.x = pos.x
    imgPos.y = pos.y
  }

  getScale = () => {
    return this.transformMatrix.elements[0] || 1
  }

  getWrapClientRect = () => {
    return this.renderManager.getClientRect()
  }

  getClientRect = () => {
    return this.clientRect
  }

  isVisible = () => {
    const wrapClientRect = this.getWrapClientRect()
    const clientRect = this.clientRect

    const needRender =
      clientRect.bottom >= wrapClientRect.top &&
      clientRect.top <= wrapClientRect.bottom &&
      clientRect.left <= wrapClientRect.right &&
      clientRect.right >= wrapClientRect.left

    return needRender
  }

  updateSize = () => {
    const width = this.container.clientWidth || 100
    const height = this.container.clientHeight || 100

    if (width !== this.width || height !== this.height) {
      this.width = width
      this.height = height
      this.updateAspectRatioConfig()
    }
  }

  updateTransform = () => {
    const wrapClientRect = this.getWrapClientRect()

    const left = this.clientRect.left - wrapClientRect.left
    const top = this.clientRect.top - wrapClientRect.top

    const offsetMatrix = get(THREE.Matrix4).makeTranslation(left, top, 0)
    this.transformMatrix.copy(this.containerMatrix).multiply(this.fitMatrix)
    this.proxyTransformMatrix.copy(offsetMatrix).multiply(this.transformMatrix)
  }

  updateViewport = () => {
    const { imgSize } = this
    const { renderer, context } = this.renderManager
    const wrapClientRect = this.getWrapClientRect()

    // proxy relative offset
    const top = this.clientRect.top - wrapClientRect.top
    const left = this.clientRect.left - wrapClientRect.left
    const bottom = wrapClientRect.bottom - this.clientRect.bottom
    // clip view region
    context.beginPath()
    context.rect(left, top, this.clientRect.width, this.clientRect.height)
    context.closePath()
    context.clip()

    // left-bottom corn
    const pos = get(THREE.Vector2, 0).set(0, imgSize.y)
    this.imgToDom(pos)
    pos.y = this.clientRect.height - pos.y

    pos.x += left
    pos.y += bottom

    const scale = this.getScale()
    const width = imgSize.x * scale
    const height = imgSize.y * scale

    renderer.setViewport(pos.x, pos.y, width, height)
    renderer.setScissor(pos.x, pos.y, width, height)
  }

  renderImage = () => {
    const { imgSize, imgEl: img } = this
    if (!img) return
    const { context } = this.renderManager

    const m = this.proxyTransformMatrix.elements
    context.setTransform(m[0], m[1], m[4], m[5], m[12], m[13])
    context.drawImage(img, 0, 0, imgSize.x, imgSize.y)
  }

  override renderFrame = () => {
    this.clientRect = this.container.getBoundingClientRect()
    if (!this.isVisible()) return

    this.updateSize()
    this.updateTransform()
    this.updateViewport()

    this.dispatchEvent({ type: ViewEvent.RENDER_BEFORE })

    this.renderImage()
    this.dispatchEvent({ type: ViewEvent.RENDER_AFTER })
  }

  override render = () => {
    if (!this.isEnable()) return
    this.renderManager.renderAll()
  }
}
