import * as THREE from 'three'

import BaseViewController from '../BaseViewController'
import type ImageViewController from './ImageViewController'

export default class ImageViewControllerProxy extends BaseViewController {
  views: ImageViewController[] = []
  // 2d
  canvas: HTMLCanvasElement
  context: CanvasRenderingContext2D
  renderer: THREE.WebGLRenderer
  width = 0
  height = 0
  // 给 view 用的
  clientRect: DOMRect = {} as DOMRect
  constructor() {
    super()
    // 2d
    const canvas = document.createElement('canvas')
    canvas.className = 'render-2d-proxy'
    Object.assign(canvas.style, {
      position: 'absolute',
      inset: '0px',
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
    })
    this.canvas = canvas
    this.context = canvas.getContext('2d')!

    // renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    this.renderer.sortObjects = false
    this.renderer.autoClear = false
    this.renderer.setClearColor(new THREE.Color(0, 0, 0), 0)

    Object.assign(this.renderer.domElement.style, {
      position: 'absolute',
      inset: '0px',
      width: '100%',
      height: '100%',
    })
  }

  addView = (view: ImageViewController) => {
    if (this.views.indexOf(view) >= 0) return
    this.views.push(view)
  }

  removeView = (view: ImageViewController) => {
    const index = this.views.indexOf(view)
    if (index >= 0) {
      this.views.splice(index, 1)
    }
  }

  mount = (container: HTMLElement) => {
    container.appendChild(this.canvas)
    container.appendChild(this.renderer.domElement)
  }

  needRender = () => {
    return this.views.filter((e) => e.isEnable()).length > 0
  }

  updateSize = () => {
    const width = this.canvas.clientWidth || 100
    const height = this.canvas.clientHeight || 100

    if (width !== this.width || height !== this.height) {
      this.width = width
      this.height = height

      this.canvas.width = width
      this.canvas.height = height

      this.renderer.setSize(width, height, false)
    }
  }

  clear = () => {
    const { context, renderer } = this

    context.clearRect(0, 0, this.width, this.height)

    renderer.setScissorTest(false)
    renderer.clear()
    renderer.setScissorTest(true)
  }

  renderFrame = () => {
    if (!this.needRender()) return

    this.clientRect = this.canvas.getBoundingClientRect()
    this.updateSize()
    this.clear()

    const { context } = this
    this.views.forEach((view) => {
      context.save()
      view.renderFrame()
      context.restore()
    })
  }
}
