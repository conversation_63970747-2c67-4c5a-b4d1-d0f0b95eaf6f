<script setup lang="ts">
import { LoadingOutlined } from '@ant-design/icons-vue'
import { useEventListener, useParentElement } from '@vueuse/core'
import { throttle } from 'lodash-es'
import * as THREE from 'three'
import {
  computed,
  effectScope,
  h,
  onMounted,
  onScopeDispose,
  onWatcherCleanup,
  ref,
  shallowRef,
  useTemplateRef,
  watch,
  watchEffect,
} from 'vue'

import { useEditor } from '@/editors/LaneLine3D/useEditor'
import type { ICameraInternal } from '@/types'

import ImageViewController from './ImageViewController'
import ProjectionPlugin from './ProjectionPlugin'
import RendererPlugin from './RendererPlugin'
import { useInjectImageViewControllerProxy } from './useImageViewControllerProxy'

const props = defineProps<{
  viewIndex: number
  cameraInternal: ICameraInternal
  cameraExternal: Array<number>
  imgUrl: string
  /** 是否可以缩放和拖动 */
  interactive?: boolean
}>()

const containerRef = useTemplateRef<HTMLDivElement>('container')

const editor = useEditor()
const geometriesObject3D = editor.geometryGroup.object
const geometry2DList = computed(() => {
  return editor.geometry2DList.list.filter(
    (item) => item.userData.shape.viewIndex === props.viewIndex,
  )
})

const renderProxy = useInjectImageViewControllerProxy()
let controller: ImageViewController

onMounted(() => {
  if (containerRef.value) {
    controller = new ImageViewController(containerRef.value, renderProxy)
    useEventListener(geometriesObject3D, 'change', () => controller.render())
    onScopeDispose(() => {
      renderProxy.removeView(controller)
    })

    const projectionPlugin = new ProjectionPlugin(geometriesObject3D)
    projectionPlugin.attach(controller)
    onScopeDispose(() => {
      projectionPlugin.detach()
    })
    const rendererPlugin = new RendererPlugin()
    rendererPlugin.attach(controller)
    onScopeDispose(() => {
      rendererPlugin.detach()
    })
    watch(
      geometry2DList,
      () => {
        console.log('geometry2DList changed', geometry2DList.value)
        rendererPlugin.updateObjects(geometry2DList.value)
        controller.render()
      },
      {
        immediate: true,
      },
    )
  }
})

const loading = ref(false)
const error = ref(false)
const imgObject = shallowRef<HTMLImageElement>()

watch(
  () => props.imgUrl,
  (url) => {
    const img = new Image()
    img.src = url
    loading.value = true
    error.value = false
    img.onload = () => {
      loading.value = false
      imgObject.value = img
    }
    img.onerror = () => {
      loading.value = false
      error.value = true
    }
  },
  {
    immediate: true,
  },
)

watchEffect(() => {
  if (!imgObject.value || imgObject.value.src !== props.imgUrl) return
  controller.setOptions({
    cameraInternal: props.cameraInternal,
    cameraExternal: props.cameraExternal,
    imgObject: imgObject.value,
  })
})

const temp = new THREE.Matrix4()
function isLeft(e: MouseEvent) {
  return e.button === 0
}
function onMousedown(e: MouseEvent) {
  if (isLeft(e)) return
  const { containerMatrix } = controller
  const startPos = new THREE.Vector2(e.clientX, e.clientY)
  const curPos = new THREE.Vector2()
  const startMatrix = containerMatrix.clone()
  const matrix = temp

  const onDocMove = throttle((e: MouseEvent) => {
    curPos.set(e.clientX, e.clientY).sub(startPos)
    matrix.makeTranslation(curPos.x, curPos.y, 0)
    containerMatrix.multiplyMatrices(matrix, startMatrix)
    controller.render()
  }, 30)
  function onDocUp() {
    document.removeEventListener('mousemove', onDocMove)
    document.removeEventListener('mouseup', onDocUp)
  }

  document.addEventListener('mousemove', onDocMove)
  document.addEventListener('mouseup', onDocUp)
}
function onZoom(e: WheelEvent) {
  const containerScale = controller.containerMatrix.elements[0] as number
  const { containerMatrix } = controller
  const zoom = e.deltaY > 0 ? 0.95 : 1.05

  const newScale = containerScale * zoom
  if (newScale < 0.1 || newScale > 10) return

  const bbox = containerRef.value!.getBoundingClientRect()
  const offsetX = e.clientX - bbox.x
  const offsetY = e.clientY - bbox.y

  const matrix = temp

  containerMatrix.premultiply(matrix.makeTranslation(-offsetX, -offsetY, 0))
  containerMatrix.premultiply(matrix.makeScale(zoom, zoom, 1))
  containerMatrix.premultiply(matrix.makeTranslation(offsetX, offsetY, 0))

  controller.render()
}
const parentRef = useParentElement()
watch(
  () => props.interactive,
  (interactive) => {
    if (!interactive) return
    const scope = effectScope()
    scope.run(() => {
      useEventListener(parentRef, 'wheel', onZoom)
      useEventListener(parentRef, 'mousedown', onMousedown)
      useEventListener(parentRef, 'contextmenu', (e) => e.preventDefault())
    })
    onWatcherCleanup(() => scope.stop())
  },
  {
    immediate: true,
  },
)

const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '24px',
  },
  spin: true,
})
</script>

<template>
  <div ref="container">
    <div
      v-if="loading"
      class="absolute inset-0 flex items-center justify-center bg-white opacity-30"
    >
      <a-spin :indicator="indicator" />
    </div>
  </div>
</template>
