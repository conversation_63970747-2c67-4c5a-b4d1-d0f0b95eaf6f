<script setup lang="ts">
import { useResizeObserver } from '@vueuse/core'
import { onMounted, useTemplateRef } from 'vue'

import { useProvideImageViewControllerProxy } from './useImageViewControllerProxy'

const containerRef = useTemplateRef<HTMLDivElement>('container')

const imageViewControllerProxy = useProvideImageViewControllerProxy()

onMounted(() => {
  if (containerRef.value) {
    imageViewControllerProxy.mount(containerRef.value)
  }
})

useResizeObserver(containerRef, () => imageViewControllerProxy.render())
</script>

<template>
  <div
    ref="container"
    class="relative h-full w-full"
  >
    <slot :controller="imageViewControllerProxy" />
  </div>
</template>
