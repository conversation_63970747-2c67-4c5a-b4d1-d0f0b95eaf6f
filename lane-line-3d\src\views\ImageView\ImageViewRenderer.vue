<script setup lang="ts">
import { useResizeObserver } from '@vueuse/core'
import { onMounted, onScopeDispose, useTemplateRef } from 'vue'

import { useProvideImageRenderManager } from './useImageRenderManager'

const containerRef = useTemplateRef<HTMLDivElement>('container')

const imageRenderManager = useProvideImageRenderManager()

onMounted(() => {
  if (containerRef.value) {
    imageRenderManager.mount(containerRef.value)
    onScopeDispose(() => {
      imageRenderManager.unmount()
    })
  }
})

useResizeObserver(containerRef, () => imageRenderManager.renderAll())
</script>

<template>
  <div
    ref="container"
    class="relative h-full w-full"
  >
    <slot :render-manager="imageRenderManager" />
  </div>
</template>
