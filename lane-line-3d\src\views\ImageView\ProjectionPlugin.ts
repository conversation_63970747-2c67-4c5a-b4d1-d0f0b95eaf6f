import * as THREE from 'three'

import { ViewEvent } from '@/const'
import { get } from '@/utils'
import { createMatrixFromCameraInternal } from '@/utils/three-utils'

import type { IOption } from './ImageViewController'
import type ImageViewController from './ImageViewController'

export default class ProjectionPlugin {
  object: THREE.Object3D
  camera: THREE.PerspectiveCamera
  imageViewController: ImageViewController | null = null
  listeners: Array<[string, (e: any) => void]> = []
  constructor(object: THREE.Object3D) {
    this.object = object
    this.camera = new THREE.PerspectiveCamera(50, 10, 1, 1000)
  }
  updateCamera = (option: IOption) => {
    const { imgObject, cameraInternal, cameraExternal } = option

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const matrixExternal = get(THREE.Matrix4).set(...cameraExternal)
    matrixExternal.premultiply(new THREE.Matrix4().makeScale(1, -1, -1))
    matrixExternal.invert()
    matrixExternal.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)
    this.camera.updateMatrixWorld()

    const matrixInternal = createMatrixFromCameraInternal(
      cameraInternal,
      imgObject.naturalWidth,
      imgObject.naturalHeight,
    )
    this.camera.projectionMatrix.copy(matrixInternal)
    this.camera.projectionMatrixInverse.copy(matrixInternal).invert()
  }
  attach = (imageViewController: ImageViewController) => {
    this.detach()
    this.imageViewController = imageViewController
    if (this.imageViewController.option) {
      this.updateCamera(this.imageViewController.option)
    }
    this.listeners = [
      [
        ViewEvent.OPTIONS_CHANGE,
        (e) => {
          this.updateCamera(e.data)
        },
      ],
      [
        ViewEvent.RENDER_AFTER,
        () => {
          this.imageViewController?.renderManager.renderer.render(this.object, this.camera)
        },
      ],
    ]
    this.listeners.forEach(([type, listener]) => {
      this.imageViewController?.addEventListener(type, listener)
    })
  }
  detach = () => {
    this.listeners.forEach(([type, listener]) => {
      this.imageViewController?.removeEventListener(type, listener)
    })
    this.listeners = []
    this.imageViewController = null
  }
}
