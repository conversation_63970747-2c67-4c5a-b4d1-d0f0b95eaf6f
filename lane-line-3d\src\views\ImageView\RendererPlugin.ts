import { ViewEvent } from '@/const'
import type Object2D from '@/objects/base/Object2D'

import type ImageViewController from './ImageViewController'

export default class RendererPlugin {
  imageViewController: ImageViewController | null = null
  listeners: Array<[string, (e: any) => void]> = []
  objects: Object2D[] = []
  constructor() {}
  updateObjects = (objects: Object2D[]) => {
    this.objects = objects
  }
  attach = (imageViewController: ImageViewController) => {
    this.detach()
    this.imageViewController = imageViewController
    this.listeners = [
      [
        ViewEvent.RENDER_AFTER,
        () => {
          const context = this.imageViewController?.proxy.context
          if (!context) return
          this.objects.forEach((object) => {
            object.render(context)
          })
        },
      ],
    ]
    this.listeners.forEach(([type, listener]) => {
      this.imageViewController?.addEventListener(type, listener)
    })
  }
  detach = () => {
    this.listeners.forEach(([type, listener]) => {
      this.imageViewController?.removeEventListener(type, listener)
    })
    this.listeners = []
    this.imageViewController = null
  }
}
