import { inject, type InjectionKey, markRaw, provide } from 'vue'

import ImageRenderManager from './ImageRenderManager'

export const context = Symbol('ImageRenderManager') as InjectionKey<ImageRenderManager>

export function useProvideImageRenderManager() {
  const renderManager = markRaw(new ImageRenderManager())
  provide(context, renderManager)
  return renderManager
}

export function useInjectImageRenderManager() {
  return inject(context)!
}
