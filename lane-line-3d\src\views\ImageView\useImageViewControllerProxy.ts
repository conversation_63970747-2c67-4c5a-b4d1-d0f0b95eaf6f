import { inject, type Injection<PERSON><PERSON>, markRaw, provide } from 'vue'

import ImageViewControllerProxy from './ImageViewControllerProxy'

export const context = Symbol('ImageViewControllerProxy') as InjectionKey<ImageViewControllerProxy>

export function useProvideImageViewControllerProxy() {
  const controller = markRaw(new ImageViewControllerProxy())
  provide(context, controller)
  return controller
}

export function useInjectImageViewControllerProxy() {
  return inject(context)!
}
