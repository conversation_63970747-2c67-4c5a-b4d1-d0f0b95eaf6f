<script setup lang="ts">
import { useEventListener, useResizeObserver } from '@vueuse/core'
import { onMounted, onScopeDispose, useTemplateRef, watch } from 'vue'

import { ViewEvent } from '@/const'
import { EditorEvent, useEditor } from '@/editors/LaneLine3D/useEditor'
import useMouseRaycastObjectEvents, { create3DRaycast } from '@/hooks/useMouseRaycastObjectEvents'
import useInspection from '@/stores/useInspection'
import useSelection from '@/stores/useSelection'

import { useProvidePointCloudMainViewController } from './usePointCloudMainViewController'

const props = withDefaults(
  defineProps<{
    backgroundColor?: string
    pixelRatio?: number
    disabled?: boolean
  }>(),
  {
    backgroundColor: '#000000',
    pixelRatio: 1,
  },
)

const editor = useEditor()
const controller = useProvidePointCloudMainViewController()

watch(
  () => props.disabled,
  (disabled) => {
    controller.enabled = !disabled
  },
  {
    immediate: true,
  },
)

const { renderer } = controller
renderer.setPixelRatio(props.pixelRatio)
watch(
  () => props.pixelRatio,
  (pixelRatio) => {
    renderer.setPixelRatio(pixelRatio)
    controller.render()
  },
)
renderer.setClearColor(props.backgroundColor)
watch(
  () => props.backgroundColor,
  (color) => {
    renderer.setClearColor(color)
    controller.render()
  },
)

const containerRef = useTemplateRef<HTMLDivElement>('container')
onMounted(() => {
  if (!containerRef.value) return
  controller.mount(containerRef.value)
  editor.emit(EditorEvent.ViewMounted, { controller })
  onScopeDispose(() => controller.unmount())
})
useResizeObserver(containerRef, () => controller.render())

const geometriesObject3D = editor.geometryGroup.object

useMouseRaycastObjectEvents(
  controller,
  create3DRaycast(
    controller.renderer.domElement,
    controller.camera,
    () => geometriesObject3D.children,
  ),
  controller.renderer.domElement,
)

const { selectGeometryById } = useSelection()
useEventListener(controller, ViewEvent.OBJECT_CLICK, (e: any) => {
  selectGeometryById(e.currentTarget?.userData.id)
})

const { isInspecting } = useInspection()
useEventListener(controller, ViewEvent.OBJECT_DBLCLICK, () => {
  // 无需设置 inspectedAnnotationId，
  // 因为 click 会修改 selection 并会触发 useInspection 内的 watch 来设置。
  isInspecting.value = true
})
</script>

<template>
  <div
    ref="container"
    class="relative"
  >
    <slot :controller="controller" />
  </div>
</template>

<style scoped lang="scss"></style>
