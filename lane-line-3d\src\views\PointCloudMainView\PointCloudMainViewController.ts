import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer'

import BaseViewController from '../BaseViewController'

export default class PointCloudMainViewController extends BaseViewController {
  container: HTMLDivElement | null = null
  scene: THREE.Scene
  camera: THREE.OrthographicCamera
  renderer: THREE.WebGLRenderer
  domRenderer: CSS2DRenderer
  controls: OrbitControls

  constructor() {
    super()
    const scene = new THREE.Scene()

    const renderer = new THREE.WebGLRenderer({ antialias: true })
    Object.assign(renderer.domElement.style, {
      position: 'absolute',
      zIndex: 0,
      width: '100%',
      height: '100%',
    })
    renderer.sortObjects = false
    renderer.autoClear = false

    // const camera = new THREE.PerspectiveCamera(35, 1, 1, 30000)
    const camera = new THREE.OrthographicCamera(1, 1, 1, 1000)
    scene.add(camera)
    camera.position.set(0, 0, 100)
    camera.up.set(0, 0, 1)
    camera.lookAt(0, 0, 0)

    const domRenderer = new CSS2DRenderer()
    Object.assign(domRenderer.domElement.style, {
      position: 'absolute',
      zIndex: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
    })

    const controls = new OrbitControls(camera, renderer.domElement)
    controls.maxDistance = 1000
    controls.minDistance = 10
    controls.addEventListener('change', () => this.render())

    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    this.domRenderer = domRenderer
    this.controls = controls
  }
  updateSize = () => {
    if (!this.container) {
      return
    }
    const width = this.container.clientWidth || 10
    const height = this.container.clientHeight || 10
    const size = this.renderer.getSize(new THREE.Vector2())
    if (width !== size.width || height !== size.height) {
      this.renderer.setSize(width, height, false)
      this.domRenderer.setSize(width, height)
      const aspect = width / height
      const zoom = 1
      const rectWidth = 40
      const rectHeight = 40
      const cameraW = Math.max(rectWidth, rectHeight * aspect)
      const cameraH = Math.max(rectHeight, rectWidth / aspect)

      this.camera.left = (-cameraW / 2) * zoom
      this.camera.right = (cameraW / 2) * zoom
      this.camera.top = (cameraH / 2) * zoom
      this.camera.bottom = (-cameraH / 2) * zoom
      this.camera.updateProjectionMatrix()
    }
  }
  renderFrame = () => {
    this.updateSize()
    this.renderer.clear(true, true, true)
    this.renderer.render(this.scene, this.camera)
    this.domRenderer.render(this.scene, this.camera)
  }
  mount = (container: HTMLDivElement) => {
    this.unmount()
    this.container = container
    container.appendChild(this.renderer.domElement)
    container.appendChild(this.domRenderer.domElement)
    this.render(true)
  }
  unmount = () => {
    if (this.container) {
      this.container.removeChild(this.renderer.domElement)
      this.container.removeChild(this.domRenderer.domElement)
      this.container = null
    }
  }
}
