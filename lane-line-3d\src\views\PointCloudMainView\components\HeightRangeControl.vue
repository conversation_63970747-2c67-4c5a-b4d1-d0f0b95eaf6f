<script lang="ts" setup>
import { RetweetOutlined } from '@ant-design/icons-vue'
import { isNumber } from 'lodash-es'

import { useEditor } from '@/editors/LaneLine3D/useEditor'
import usePointsMaterialControl from '@/stores/usePointsMaterialControl'

const editor = useEditor()
const pointInfo = editor.pointCloud.pointInfo
const { config: pointConfig } = usePointsMaterialControl()

function formatter(value: any) {
  const n = ('' + value).split('.')
  if (n[1] && n[1].length > 1) {
    return Number(value).toFixed(1)
  } else {
    return value
  }
}
function verify() {
  const heightRange = pointConfig.heightRange
  if (!isNumber(heightRange[0])) {
    heightRange[0] = pointInfo.min.z
  }
  if (!isNumber(heightRange[1])) {
    heightRange[1] = pointInfo.max.z
  }
}
function onBlur() {
  verify()
}
function onReset() {
  pointConfig.heightRange[0] = pointInfo.min.z
  pointConfig.heightRange[1] = pointInfo.max.z
}
</script>

<template>
  <div>
    <div class="mb-1">
      <label>标注高度:</label>
    </div>
    <div class="clear-both">
      <a-input-number
        v-model:value="pointConfig.heightRange[0]"
        class="w-16"
        :formatter="formatter"
        :max="pointConfig.heightRange[1]"
        :min="pointInfo.min.z"
        size="small"
        :step="0.1"
        @blur="onBlur"
      />
      <span>~</span>
      <a-input-number
        v-model:value="pointConfig.heightRange[1]"
        class="w-16"
        :formatter="formatter"
        :max="pointInfo.max.z"
        :min="pointConfig.heightRange[0]"
        size="small"
        :step="0.1"
        @blur="onBlur"
      />
      <a-button
        class="float-right"
        size="small"
        title="重置"
        type="text"
        @click="onReset"
      >
        <template #icon>
          <RetweetOutlined />
        </template>
      </a-button>
    </div>
  </div>
</template>

<style lang="scss"></style>
