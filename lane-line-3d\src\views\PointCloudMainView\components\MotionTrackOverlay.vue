<script lang="ts" setup>
import * as THREE from 'three'
import { computed, watch } from 'vue'

import CSS2DObjectWrapper from '@/components/CSS2DObjectWrapper.vue'

import useRenderObject from '../hooks/useRenderObject'

const props = defineProps<{
  poses: {
    x: number
    y: number
    z: number
    w: number
    pose_x: number
    pose_y: number
    pose_z: number
  }[]
}>()

const activeIndex = defineModel<number>()

const positions = computed(() => {
  const localPoint = new THREE.Vector3(0, 0, 0)

  return props.poses.map((pose) => {
    const quaternion = new THREE.Quaternion(pose.x, pose.y, pose.z, pose.w)
    const translation = new THREE.Vector3(pose.pose_x, pose.pose_y, pose.pose_z)

    // 应用旋转
    const rotatedPoint = localPoint.clone().applyQuaternion(quaternion)

    // 加上平移
    const worldPoint = rotatedPoint.add(translation)

    return worldPoint.toArray()
  })
})

const group = new THREE.Object3D()

useRenderObject(group)
watch(
  positions,
  () => {
    group.dispatchEvent({ type: 'change' })
  },
  {
    deep: true,
    flush: 'post',
  },
)
</script>

<template>
  <CSS2DObjectWrapper
    v-for="(pos, index) in positions"
    :key="index"
    class="pointer-events-auto flex h-6 w-6 cursor-pointer items-center justify-center rounded-full border-2 border-white text-xs font-semibold leading-none text-white [&.active]:border-red-500 [&.active]:text-red-500"
    :class="{ active: activeIndex === index }"
    :parent="group"
    :position="pos"
    @click="activeIndex = index"
  >
    {{ index + 1 }}
  </CSS2DObjectWrapper>
</template>
