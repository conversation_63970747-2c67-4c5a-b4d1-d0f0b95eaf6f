<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import * as THREE from 'three'
import { ref } from 'vue'

import ObjectTooltip from '@/components/ObjectTooltip.vue'
import { ViewEvent } from '@/const'

import { useInjectPointCloudMainViewController } from '../usePointCloudMainViewController'

const controller = useInjectPointCloudMainViewController()!

const data = ref({
  geometryId: '',
  clientX: 0,
  clientY: 0,
})

useEventListener(
  controller,
  ViewEvent.OBJECT_HOVER,
  (event: { currentTarget: THREE.Object3D | null; mouseEvent: MouseEvent }) => {
    const { currentTarget, mouseEvent } = event
    if (currentTarget) {
      const { clientX, clientY } = mouseEvent
      data.value = {
        geometryId: currentTarget.userData.id,
        clientX,
        clientY,
      }
    } else {
      data.value = {
        geometryId: '',
        clientX: 0,
        clientY: 0,
      }
    }
  },
)
</script>

<template>
  <ObjectTooltip v-bind="data" />
</template>
