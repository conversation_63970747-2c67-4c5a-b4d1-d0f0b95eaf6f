<script setup lang="ts">
import { flip, offset, shift, useFloating } from '@floating-ui/vue'
import { useEventListener } from '@vueuse/core'
import * as THREE from 'three'
import { computed, ref, shallowRef } from 'vue'

import { ViewEvent } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'
import { getAllClassAttrs } from '@/utils/classType'

import { useInjectPointCloudMainViewController } from '../usePointCloudMainViewController'

const controller = useInjectPointCloudMainViewController()!
const editor = useEditor()

const createVirtualElement = (clientX: number, clientY: number) => {
  return {
    getBoundingClientRect() {
      return {
        width: 0,
        height: 0,
        x: clientX,
        y: clientY,
        left: clientX,
        right: clientX,
        top: clientY,
        bottom: clientY,
      }
    },
  }
}

const focusedObject = shallowRef<THREE.Object3D | null>(null)

const reference = ref(createVirtualElement(0, 0))
const tooltipEl = ref<HTMLDivElement>()
const { floatingStyles } = useFloating(reference, tooltipEl, {
  middleware: [offset(12), flip(), shift()],
})

const config = computed(() => {
  if (!focusedObject.value) {
    return null
  }
  const geometryId = focusedObject.value.userData.id
  const trackObject = editor.utils.getTrackObjectByGeometryId(geometryId)
  const classId = trackObject?.classId
  if (!classId) {
    return null
  }
  const classType = editor.taskData.labels.find((label) => label.id === classId)
  if (!classType) {
    return null
  }

  const geometry = trackObject.geometries.find((item) => item.id === geometryId)
  if (!geometry) {
    return null
  }
  const values = Object.assign({}, trackObject.classData, geometry.classData)
  return {
    classType,
    table: getAllClassAttrs(classType, geometry?.type)
      .filter((attr) => values.hasOwnProperty(attr.id))
      .map((attr) => {
        return {
          label: attr.name,
          value: values[attr.id],
        }
      }),
  }
})

useEventListener(
  controller,
  ViewEvent.OBJECT_HOVER,
  (event: { currentTarget: THREE.Object3D | null; mouseEvent: MouseEvent }) => {
    const { currentTarget, mouseEvent } = event
    focusedObject.value = currentTarget
    if (currentTarget) {
      const { clientX, clientY } = mouseEvent
      reference.value = createVirtualElement(clientX, clientY)
    }
  },
)
</script>

<template>
  <Teleport to="body">
    <div
      v-show="!!focusedObject"
      ref="tooltipEl"
      class="object-tooltip"
      :style="floatingStyles"
    >
      <div v-if="focusedObject && !config">无标签</div>
      <template v-else-if="focusedObject && config">
        <div class="font-bold text-red-500">{{ config.classType.name }}</div>
        <table>
          <tbody>
            <tr
              v-for="(item, i) in config.table"
              :key="i"
            >
              <td>{{ item.label }}：</td>
              <td class="text-right">{{ item.value }}</td>
            </tr>
          </tbody>
        </table>
      </template>
    </div>
  </Teleport>
</template>

<style lang="less">
.object-tooltip {
  z-index: 999;
  padding: 4px 8px;
  color: rgb(255 255 255 / 85%);
  background: #0008;
  border: 1px solid rgb(255 255 255 / 85%);
  border-radius: 2px;
}
</style>
