<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import { message } from 'ant-design-vue'
import * as THREE from 'three'
import { computed, ref, watch } from 'vue'

import InteractivePoints from '@/components/InteractivePoints.vue'
import { GeometryTypes, ViewEvent } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'
import useActionManager from '@/stores/useActionManager'
import useSelection from '@/stores/useSelection'
import { canvasToWorld, worldToCanvas } from '@/utils/three-utils'

import usePolylinePointSelection from '../hooks/usePolylinePointSelection'
import type PointCloudMainViewController from '../PointCloudMainViewController'

const props = defineProps<{
  controller: PointCloudMainViewController
}>()

const { selectedGeometries } = useSelection()
const editor = useEditor()

const editingObject = computed(() => {
  if (selectedGeometries.value.length !== 1) return
  const target = selectedGeometries.value[0]
  if (target.type !== GeometryTypes.Polyline3D && target.type !== GeometryTypes.Polygon3D) {
    return
  }
  return target
})
const selectedPointIndex = usePolylinePointSelection().selectedPointIndex
watch(
  () => editingObject.value?.id,
  () => {
    selectedPointIndex.value = -1
  },
)

const renderIndex = ref(0)

useEventListener(
  () => props.controller,
  ViewEvent.RENDER_AFTER,
  () => {
    renderIndex.value++
  },
)

const planePoints = computed(() => {
  if (!editingObject.value) return []
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  renderIndex.value // force update
  return editingObject.value.shape.points.map((p) => {
    const canvasPos = worldToCanvas(
      props.controller.renderer.domElement,
      props.controller.camera,
      new THREE.Vector3(p[0], p[1], p[2]),
    )
    return [canvasPos.x, canvasPos.y] as [number, number]
  })
})

const handleClickLine = (event: MouseEvent, index: number) => {
  if (!editingObject.value) return
  const targetId = editingObject.value.id
  const object3D = editor.geometryGroup.object.children.find(
    (item) => item.userData.id === targetId,
  )
  if (!object3D) return
  editor.utils.updateGeometryById(targetId, (geometry) => {
    if (geometry.type !== GeometryTypes.Polyline3D && geometry.type !== GeometryTypes.Polygon3D) {
      return
    }
    const rect = props.controller.renderer.domElement.getBoundingClientRect()
    const point = canvasToWorld(
      props.controller.renderer.domElement,
      props.controller.camera,
      new THREE.Vector2(event.clientX - rect.left, event.clientY - rect.top),
      object3D,
    )
    geometry.shape.points.splice(index + 1, 0, point.toArray())
  })
}

useActionManager().useRegisterAction('del', (next) => {
  if (!editingObject.value || selectedPointIndex.value === -1) {
    next()
    return
  }
  editor.utils.updateGeometryById(editingObject.value.id, (geometry) => {
    if (geometry.type !== GeometryTypes.Polyline3D && geometry.type !== GeometryTypes.Polygon3D) {
      return
    }
    if (geometry.type === GeometryTypes.Polyline3D && geometry.shape.points.length <= 2) {
      message.warning('折线至少需要两个点')
      return
    }
    if (geometry.type === GeometryTypes.Polygon3D && geometry.shape.points.length <= 3) {
      message.warning('多边形至少需要三个点')
      return
    }
    geometry.shape.points.splice(selectedPointIndex.value, 1)
    selectedPointIndex.value = Math.min(selectedPointIndex.value, geometry.shape.points.length - 1)
  })
})
</script>

<template>
  <InteractivePoints
    v-if="editingObject"
    :key="editingObject.id"
    v-model="selectedPointIndex"
    class="z-10"
    :close="editingObject.type === GeometryTypes.Polygon3D"
    :points="planePoints"
    @click-line="handleClickLine"
  />
</template>

<style scoped lang="scss"></style>
