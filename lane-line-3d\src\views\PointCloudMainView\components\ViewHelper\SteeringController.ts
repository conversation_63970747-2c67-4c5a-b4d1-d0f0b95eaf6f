import * as THREE from 'three'
import { type OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

const viewPosition = {
  posX: 'posX',
  posY: 'posY',
  posZ: 'posZ',
  negX: 'negX',
  negY: 'negY',
  negZ: 'negZ',
}
export type ViewType = keyof typeof viewPosition

export default class ViewController {
  viewCamera: THREE.PerspectiveCamera | THREE.OrthographicCamera
  controls: OrbitControls
  private dummy: THREE.Object3D
  private clock: THREE.Clock

  private turnRate: number = 2 * Math.PI
  private radius: number = 0
  private targetPosition: THREE.Vector3
  private targetQuaternion: THREE.Quaternion
  private q1: THREE.Quaternion
  private q2: THREE.Quaternion
  private m: THREE.Matrix4
  private offset: THREE.Vector3
  private quat: THREE.Quaternion
  private quatInverse: THREE.Quaternion
  private spherical: THREE.Spherical
  constructor(
    viewCamera: THREE.PerspectiveCamera | THREE.OrthographicCamera,
    controls: OrbitControls,
  ) {
    this.viewCamera = viewCamera
    this.controls = controls

    this.dummy = new THREE.Object3D()
    this.dummy.up.copy(this.viewCamera.up)

    this.clock = new THREE.Clock()

    this.m = new THREE.Matrix4()

    this.turnRate = 2 * Math.PI // turn rate in angles per second

    this.targetPosition = new THREE.Vector3()
    this.targetQuaternion = new THREE.Quaternion()

    this.q1 = new THREE.Quaternion()
    this.q2 = new THREE.Quaternion()

    this.offset = new THREE.Vector3()
    this.quat = new THREE.Quaternion().setFromUnitVectors(
      this.viewCamera.up,
      new THREE.Vector3(0, 1, 0),
    )
    this.quatInverse = this.quat.clone().invert()
    this.spherical = new THREE.Spherical()
    this.radius = 0
  }
  view(type: ViewType): Promise<any> {
    return new Promise((resolve) => {
      const focusPoint = this.controls.target
      const _camera = this.viewCamera
      switch (type) {
        case viewPosition.posX:
          this.targetPosition.set(1, 0, 0)
          break
        case viewPosition.posY:
          this.targetPosition.set(0, 1, 0)
          break
        case viewPosition.posZ:
          this.targetPosition.set(0, 0, 1)
          break
        case viewPosition.negX:
          this.targetPosition.set(-1, 0, 0)
          break
        case viewPosition.negY:
          this.targetPosition.set(0, -1, 0)
          break
        case viewPosition.negZ:
          this.targetPosition.set(0, 0, -1)
          break

        default:
          console.error('ViewHelper: Invalid axis.')
      }
      this.offset.copy(this.targetPosition)
      this.offset.applyQuaternion(this.quat)
      this.spherical.setFromVector3(this.offset)
      this.spherical.phi = Math.max(
        this.controls.minPolarAngle,
        Math.min(this.controls.maxPolarAngle, this.spherical.phi),
      )
      this.spherical.makeSafe()
      this.offset.setFromSpherical(this.spherical)
      this.offset.applyQuaternion(this.quatInverse)
      this.targetPosition.set(0, 0, 0).add(this.offset)
      this.m.lookAt(this.targetPosition, new THREE.Vector3(0, 0, 0), _camera.up)
      this.targetQuaternion.setFromRotationMatrix(this.m)
      this.radius = _camera.position.distanceTo(focusPoint)
      this.targetPosition.multiplyScalar(this.radius).add(focusPoint)
      this.dummy.position.copy(focusPoint)
      this.dummy.lookAt(_camera.position)
      this.q1.copy(this.dummy.quaternion)
      this.dummy.lookAt(this.targetPosition)
      this.q2.copy(this.dummy.quaternion)
      // clock
      this.clock = new THREE.Clock()
      this._animate(resolve)
    })
  }
  _animate(resolve: (value: any) => void) {
    const delta = this.clock.getDelta()
    const step = delta * this.turnRate
    const focusPoint = this.controls.target
    const _camera = this.viewCamera
    // animate position by doing a slerp and then scaling the position on the unit sphere
    this.q1.rotateTowards(this.q2, step)
    _camera.position
      .set(0, 0, 1)
      .applyQuaternion(this.q1)
      .multiplyScalar(this.radius)
      .add(focusPoint)
    // animate orientation
    _camera.quaternion.rotateTowards(this.targetQuaternion, step)
    this.controls.update()
    if (this.q1.angleTo(this.q2) < 1e-6) {
      resolve(true)
    } else {
      requestAnimationFrame(this._animate.bind(this, resolve))
    }
  }
}
