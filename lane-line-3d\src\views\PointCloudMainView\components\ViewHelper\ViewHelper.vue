<script lang="ts" setup>
import { useEventListener } from '@vueuse/core'
import { Fragment, onBeforeUnmount, watch } from 'vue'

import { useInjectPointCloudMainViewController } from '../../usePointCloudMainViewController'
import ViewHelperRenderer from './ViewHelperRenderer'

const props = withDefaults(
  defineProps<{
    dim?: number
  }>(),
  {
    dim: 128,
  },
)

const controller = useInjectPointCloudMainViewController()!
const viewHelperRenderer = new ViewHelperRenderer(
  controller.renderer,
  controller.camera,
  controller.controls,
  { right: 0, bottom: 0 },
  props.dim,
)
watch(
  () => props.dim,
  (dim) => {
    viewHelperRenderer.dim = dim
    controller.render()
  },
)
const renderViewHelper = () => viewHelperRenderer.render()
controller.addSubRender(renderViewHelper)
onBeforeUnmount(() => {
  controller.removeSubRender(renderViewHelper)
})
const dom = controller.renderer.domElement
useEventListener(dom, 'pointerdown', (e: MouseEvent) => {
  const handled = viewHelperRenderer.handleClick(e)
  if (handled) {
    e.stopPropagation()
    dom.addEventListener(
      'pointerup',
      (e2) => {
        e2.stopPropagation()
      },
      {
        once: true,
      },
    )
  }
})
</script>

<template>
  <Fragment />
</template>
