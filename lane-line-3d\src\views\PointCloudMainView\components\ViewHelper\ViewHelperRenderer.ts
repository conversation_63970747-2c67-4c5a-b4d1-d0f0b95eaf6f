import * as THREE from 'three'
import { type OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

import SteeringController from './SteeringController'

const vpTemp = new THREE.Vector4()

const viewPosition = {
  posX: 'posX',
  posY: 'posY',
  posZ: 'posZ',
  negX: 'negX',
  negY: 'negY',
  negZ: 'negZ',
}

export default class ViewHelper extends THREE.Object3D {
  renderer: THREE.WebGLRenderer
  mainCamera: THREE.PerspectiveCamera | THREE.OrthographicCamera
  pos: { right: number; bottom: number }
  dim: number = 128

  steeringController: SteeringController

  camera: THREE.OrthographicCamera
  private raycaster: THREE.Raycaster
  private mouse: THREE.Vector2

  private posXAxisHelper: THREE.Sprite
  private posYAxisHelper: THREE.Sprite
  private posZAxisHelper: THREE.Sprite
  private negXAxisHelper: THREE.Sprite
  private negYAxisHelper: THREE.Sprite
  private negZAxisHelper: THREE.Sprite
  private point: THREE.Vector3

  private interactiveObjects: THREE.Sprite[]

  constructor(
    renderer: THREE.WebGLRenderer,
    mainCamera: THREE.PerspectiveCamera | THREE.OrthographicCamera,
    controls: OrbitControls,
    pos: { right: number; bottom: number },
    dim: number,
  ) {
    super()
    this.renderer = renderer
    this.mainCamera = mainCamera
    this.steeringController = new SteeringController(mainCamera, controls)
    this.pos = pos
    this.dim = dim

    const color1 = new THREE.Color('#ff3653')
    const color2 = new THREE.Color('#8adb00')
    const color3 = new THREE.Color('#2c8fff')

    const interactiveObjects: THREE.Sprite[] = []
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()

    this.camera = new THREE.OrthographicCamera(-2, 2, 2, -2, 0, 4)
    this.camera.position.set(0, 0, 2)

    const geometry = new THREE.BoxGeometry(0.8, 0.05, 0.05).translate(0.4, 0, 0)

    const xAxis = new THREE.Mesh(geometry, getAxisMaterial(color1))
    const yAxis = new THREE.Mesh(geometry, getAxisMaterial(color2))
    const zAxis = new THREE.Mesh(geometry, getAxisMaterial(color3))

    yAxis.rotation.z = Math.PI / 2
    zAxis.rotation.y = -Math.PI / 2

    this.add(xAxis)
    this.add(zAxis)
    this.add(yAxis)

    const posXAxisHelper = new THREE.Sprite(getSpriteMaterial(color1, 'X'))
    posXAxisHelper.userData.type = viewPosition.posX
    const posYAxisHelper = new THREE.Sprite(getSpriteMaterial(color2, 'Y'))
    posYAxisHelper.userData.type = viewPosition.posY
    const posZAxisHelper = new THREE.Sprite(getSpriteMaterial(color3, 'Z'))
    posZAxisHelper.userData.type = viewPosition.posZ
    const negXAxisHelper = new THREE.Sprite(getSpriteMaterial(color1))
    negXAxisHelper.userData.type = viewPosition.negX
    const negYAxisHelper = new THREE.Sprite(getSpriteMaterial(color2))
    negYAxisHelper.userData.type = viewPosition.negY
    const negZAxisHelper = new THREE.Sprite(getSpriteMaterial(color3))
    negZAxisHelper.userData.type = viewPosition.negZ

    posXAxisHelper.position.x = 1
    posYAxisHelper.position.y = 1
    posZAxisHelper.position.z = 1
    negXAxisHelper.position.x = -1
    negXAxisHelper.scale.setScalar(0.8)
    negYAxisHelper.position.y = -1
    negYAxisHelper.scale.setScalar(0.8)
    negZAxisHelper.position.z = -1
    negZAxisHelper.scale.setScalar(0.8)

    this.add(posXAxisHelper)
    this.add(posYAxisHelper)
    this.add(posZAxisHelper)
    this.add(negXAxisHelper)
    this.add(negYAxisHelper)
    this.add(negZAxisHelper)
    this.posXAxisHelper = posXAxisHelper
    this.posYAxisHelper = posYAxisHelper
    this.posZAxisHelper = posZAxisHelper
    this.negXAxisHelper = negXAxisHelper
    this.negYAxisHelper = negYAxisHelper
    this.negZAxisHelper = negZAxisHelper

    interactiveObjects.push(posXAxisHelper)
    interactiveObjects.push(posYAxisHelper)
    interactiveObjects.push(posZAxisHelper)
    interactiveObjects.push(negXAxisHelper)
    interactiveObjects.push(negYAxisHelper)
    interactiveObjects.push(negZAxisHelper)
    this.interactiveObjects = interactiveObjects
    this.point = new THREE.Vector3()

    function getAxisMaterial(color: THREE.Color) {
      return new THREE.MeshBasicMaterial({ color: color, toneMapped: false })
    }

    function getSpriteMaterial(color: THREE.Color, text: string | null = null) {
      const canvas = document.createElement('canvas')
      canvas.width = 64
      canvas.height = 64

      const context: CanvasRenderingContext2D = canvas.getContext('2d') as CanvasRenderingContext2D
      context.beginPath()
      context.arc(32, 32, 16, 0, 2 * Math.PI)
      context.closePath()
      context.fillStyle = color.getStyle()
      context.fill()

      if (text !== null) {
        context.font = '24px Arial'
        context.textAlign = 'center'
        context.fillStyle = '#000000'
        context.fillText(text, 32, 41)
      }

      const texture = new THREE.CanvasTexture(canvas)

      return new THREE.SpriteMaterial({
        map: texture,
        // depthWrite: false,
        // transparent: true,
        depthTest: false,
        toneMapped: true,
      })
    }
  }
  render() {
    this.quaternion.copy(this.mainCamera.quaternion).invert()
    this.updateMatrixWorld()

    this.point.set(0, 0, 1)
    this.point.applyQuaternion(this.mainCamera.quaternion)

    if (this.point.x >= 0) {
      this.posXAxisHelper.material.opacity = 1
      this.negXAxisHelper.material.opacity = 0.5
    } else {
      this.posXAxisHelper.material.opacity = 0.5
      this.negXAxisHelper.material.opacity = 1
    }

    if (this.point.y >= 0) {
      this.posYAxisHelper.material.opacity = 1
      this.negYAxisHelper.material.opacity = 0.5
    } else {
      this.posYAxisHelper.material.opacity = 0.5
      this.negYAxisHelper.material.opacity = 1
    }

    if (this.point.z >= 0) {
      this.posZAxisHelper.material.opacity = 1
      this.negZAxisHelper.material.opacity = 0.5
    } else {
      this.posZAxisHelper.material.opacity = 0.5
      this.negZAxisHelper.material.opacity = 1
    }
    const x = this.renderer.domElement.offsetWidth - this.dim - this.pos.right
    const y = this.pos.bottom
    const renderer = this.renderer
    renderer.clearDepth()

    renderer.getViewport(vpTemp)
    renderer.setViewport(x, y, this.dim, this.dim)

    renderer.render(this, this.camera)

    renderer.setViewport(vpTemp.x, vpTemp.y, vpTemp.z, vpTemp.w)
  }
  handleClick(event: MouseEvent) {
    const { offsetX, offsetY } = event
    const { offsetWidth, offsetHeight } = this.renderer.domElement
    this.mouse.x = ((offsetX - (offsetWidth - this.dim - this.pos.right)) / this.dim) * 2 - 1
    this.mouse.y = -((offsetY - (offsetHeight - this.dim - this.pos.bottom)) / this.dim) * 2 + 1

    this.raycaster.setFromCamera(this.mouse, this.camera)

    const intersects = this.raycaster.intersectObjects(this.interactiveObjects)
    if (intersects.length > 0) {
      const intersection = intersects[0]
      const object = intersection.object
      this.steeringController.view(object.userData.type)
      return true
    } else {
      return false
    }
  }
}
