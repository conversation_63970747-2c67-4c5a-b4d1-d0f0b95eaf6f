import * as THREE from 'three'
import { type MaybeRefOrGetter, shallowRef, toValue, triggerRef, watch } from 'vue'
const material = new THREE.MeshBasicMaterial({
  color: 0x88cc88,
  wireframe: false,
  side: THREE.DoubleSide,
})

export default function useHeightMap(
  urls: MaybeRefOrGetter<{ heightMapUrl: string; heightMapMetaUrl: string }>,
) {
  const planeGeometry = new THREE.PlaneGeometry(9999, 9999, 1, 1)
  const object = shallowRef<THREE.Mesh>(new THREE.Mesh(planeGeometry, material))

  const heightData = shallowRef<Uint8Array | null>(null)
  const heightMeta = shallowRef<any>(null)

  async function loadHeightMap(urls: { heightMapUrl: string; heightMapMetaUrl: string }) {
    try {
      const metaResponse = await fetch(urls.heightMapMetaUrl)
      const meta = await metaResponse.json()
      heightMeta.value = meta

      const rawResponse = await fetch(urls.heightMapUrl)
      const buffer = await rawResponse.arrayBuffer()
      const data = new Uint8Array(buffer)
      heightData.value = data
      updateHeightMap(data, meta)
      triggerRef(object)
    } catch (error) {
      console.error('Failed to load heightmap:', error)
    }
  }

  /**
   * 根据x和y坐标获取高度值
   * @param x X坐标
   * @param y Y坐标
   * @param defaultHeight 默认高度值，当无法获取高度时返回
   * @returns 高度值(z坐标)
   */
  function getHeight(x: number, y: number, defaultHeight: number = 0): number {
    // 如果没有高度数据或元数据，返回默认高度
    if (!heightData.value || !heightMeta.value) {
      return defaultHeight
    }

    const meta = heightMeta.value
    const data = heightData.value

    // 从元数据中获取必要的信息
    const { x_min, y_max, width, height, height_min, height_max, resolution } = meta

    // 计算在高度图中的行列索引
    const col = Math.floor((x - x_min) / resolution)
    const row = Math.floor((y_max - y) / resolution)

    // 检查是否在高度图范围内
    if (col < 0 || col >= width || row < 0 || row >= height) {
      return defaultHeight
    }

    // 计算在一维数组中的索引
    const index = row * width + col

    // 获取高度值并归一化
    const pixel = data[index]
    const normalized = pixel / 255
    const heightValue = height_min + normalized * (height_max - height_min)

    return heightValue
  }

  function updateHeightMap(heightData: Uint8Array, meta: any) {
    const {
      x_min,
      y_max,
      width: fullWidth,
      height: fullHeight,
      height_min,
      height_max,
      resolution,
    } = meta

    const step = 5 // 每隔几个像素采样一次，例如 step=5 表示每隔 5 个像素采样（降低分辨率）
    const width = Math.floor(fullWidth / step)
    const height = Math.floor(fullHeight / step)
    const heightRange = height_max - height_min

    const geometry = new THREE.BufferGeometry()
    const positions = new Float32Array(width * height * 3)

    for (let row = 0; row < height; row++) {
      for (let col = 0; col < width; col++) {
        const rawRow = row * step
        const rawCol = col * step
        const idx = rawRow * fullWidth + rawCol
        const pixel = heightData[idx]
        const normalized = pixel / 255
        const h = height_min + normalized * heightRange

        const x = x_min + rawCol * resolution
        const y = y_max - rawRow * resolution
        const z = h

        const i3 = (row * width + col) * 3
        positions[i3] = x
        positions[i3 + 1] = y
        positions[i3 + 2] = z
      }
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))

    // 生成地形网格的索引（构建为三角网）
    const indices = new Uint32Array((width - 1) * (height - 1) * 6)
    for (let row = 0; row < height - 1; row++) {
      for (let col = 0; col < width - 1; col++) {
        const a = row * width + col
        const b = a + 1
        const c = a + width
        const d = c + 1
        // 两个三角形
        const i6 = (row * (width - 1) + col) * 6
        indices[i6] = a
        indices[i6 + 1] = c
        indices[i6 + 2] = b
        indices[i6 + 3] = b
        indices[i6 + 4] = c
        indices[i6 + 5] = d
      }
    }
    geometry.setIndex(new THREE.BufferAttribute(indices, 1))
    geometry.computeVertexNormals()

    object.value.geometry.dispose()
    object.value.geometry = geometry
    object.value.geometry.computeBoundingSphere()
    object.value.geometry.computeBoundingBox()
  }

  watch(
    () => toValue(urls),
    (urls) => {
      if (urls.heightMapUrl) {
        loadHeightMap(urls)
      }
    },
    {
      immediate: true,
    },
  )

  return {
    object,
    getHeight, // 暴露getHeight函数
  }
}
