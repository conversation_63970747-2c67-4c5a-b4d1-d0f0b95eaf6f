import { useEventListener } from '@vueuse/core'
import * as THREE from 'three'
import { type MaybeRefOrGetter, onWatcherCleanup, toValue, watch } from 'vue'

import { useInjectPointCloudMainViewController } from '../usePointCloudMainViewController'

export default function useRenderObject(
  object: MaybeRefOrGetter<THREE.Object3D | null | undefined>,
) {
  const controller = useInjectPointCloudMainViewController()!
  const render = () => controller.render()
  watch(
    () => toValue(object),
    (object) => {
      if (!object) return
      controller.scene.add(object)
      render()
      onWatcherCleanup(() => {
        controller.scene.remove(object)
        render()
      })
    },
    {
      immediate: true,
    },
  )
  useEventListener(object, 'change', render)
}
