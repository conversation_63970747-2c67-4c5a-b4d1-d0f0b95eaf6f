import { inject, type InjectionK<PERSON>, markRaw, provide } from 'vue'

import PointCloudMainViewController from './PointCloudMainViewController'

const context = Symbol('PointCloudMainViewController') as InjectionKey<PointCloudMainViewController>

export function useProvidePointCloudMainViewController() {
  const controller = markRaw(new PointCloudMainViewController())
  provide(context, controller)
  return controller
}

export function useInjectPointCloudMainViewController() {
  return inject(context)!
}
