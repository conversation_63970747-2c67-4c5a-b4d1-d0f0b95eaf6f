<script setup lang="ts">
import {
  ArrowDownOutlined,
  <PERSON><PERSON><PERSON>tOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>UpOutlined,
  Rotate<PERSON>eftOutlined,
  RotateRightOutlined,
} from '@ant-design/icons-vue'
import { useEventListener, useResizeObserver } from '@vueuse/core'
import * as THREE from 'three'
import { computed, markRaw, onMounted, onScopeDispose, ref, watch } from 'vue'

import { GeometryTypes } from '@/const'
import useEditor, { EditorEvent, type IGeometry } from '@/editors/LaneLine3D/useEditor'
import { bindLocale } from '@/i18n'

import * as locale from './lang'
import PointCloudSideViewController, { type AxisType } from './PointCloudSideViewController'

// ***************Props and Emits***************
const props = defineProps<{
  pointCloud: THREE.Points
  axis: AxisType
  target: THREE.Object3D | null | undefined
  disabled?: boolean
  /** 多边形没有明确的中点不可以启用旋转，但长方体可以 */
  rotatable?: boolean
}>()
// *********************************************

const containerRef = ref<HTMLDivElement | null>(null)

const controller = markRaw(new PointCloudSideViewController(props.pointCloud, props.axis))
const $$ = bindLocale(locale)

const title = computed(() => {
  let title = ''
  switch (props.axis) {
    case 'z':
      title = $$('side_overhead')
      break
    case '-y':
      title = $$('side_side')
      break
    case '-x':
      title = $$('side_near')
      break
  }
  return title
})

//**************life hook******************
onMounted(() => {
  if (containerRef.value) {
    controller.mount(containerRef.value)
    editor.emit(EditorEvent.ViewMounted, { controller })
    useEventListener(props.pointCloud, 'change', () => controller.render())
    onScopeDispose(() => {
      controller.unmount()
    })
  }
})
// ************************************

function fitObject() {
  controller.camera.zoom = 1
  controller.enableFit = true
  controller.fitAndRender()
}

let actionTimer = -1 as any

function onAction(fn: () => void) {
  if (controller.enableFit) {
    fn()
    fitObject()
  }
  if (actionTimer >= 0) return

  document.addEventListener('mouseup', onDocMouseUp)
  actionTimer = setInterval(() => {
    if (controller.enableFit) {
      fn()
      fitObject()
    }
  }, 50)
}

function onDocMouseUp() {
  if (actionTimer < 0) return
  document.removeEventListener('mouseup', onDocMouseUp)
  clearInterval(actionTimer)
  actionTimer = -1
}

useResizeObserver(containerRef, () => controller.render())

watch(
  () => props.target,
  (target) => {
    controller.setTargetObject(target || null)
  },
  {
    immediate: true,
  },
)

const editor = useEditor()

const updateTarget = (cb: (target: IGeometry) => void) => {
  if (!props.target) return
  editor.utils.updateGeometryById(props.target.userData.id, cb)
}

const handleRotationZLeft = () => {
  console.log('rotationZLeft')
}

const handleRotationZRight = () => {
  console.log('rotationZRight')
}

const OFFSET = 0.02

const addX = (point: [number, number, number]) => {
  point[0] += OFFSET
}
const subX = (point: [number, number, number]) => {
  point[0] -= OFFSET
}
const addY = (point: [number, number, number]) => {
  point[1] += OFFSET
}
const subY = (point: [number, number, number]) => {
  point[1] -= OFFSET
}
const addZ = (point: [number, number, number]) => {
  point[2] += OFFSET
}
const subZ = (point: [number, number, number]) => {
  point[2] -= OFFSET
}

const handleTranslateXMinus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      subX(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => subX(p))
    }
  })
}

const handleTranslateXPlus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      addX(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => addX(p))
    }
  })
}

const handleTranslateYMinus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      subY(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => subY(p))
    }
  })
}

const handleTranslateYPlus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      addY(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => addY(p))
    }
  })
}

const handleTranslateZMinus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      subZ(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => subZ(p))
    }
  })
}

const handleTranslateZPlus = () => {
  updateTarget((geo) => {
    if (geo.type === GeometryTypes.Point3D) {
      addZ(geo.shape.point)
    } else if (geo.type === GeometryTypes.Polyline3D || geo.type === GeometryTypes.Polygon3D) {
      geo.shape.points.forEach((p) => addZ(p))
    }
  })
}

const actions = {
  A: handleTranslateXMinus,
  D: handleTranslateXPlus,
  W: handleTranslateZPlus,
  S: handleTranslateZMinus,
  Q: handleTranslateYPlus,
  E: handleTranslateYMinus,
  Z: handleRotationZLeft,
  X: handleRotationZRight,
}
</script>

<template>
  <div class="side-view relative h-full select-none overflow-hidden text-[#dee5eb]">
    <div
      ref="containerRef"
      class="h-full"
      @contextmenu.stop
      @dblclick="fitObject"
    />
    <div class="absolute left-2.5 top-2.5 text-xs">{{ title }}</div>
    <div
      v-if="!!target"
      class="pointer-events-none absolute bottom-2.5 left-0 right-0 h-5 text-center"
    >
      <template v-if="axis === 'z'">
        <template v-if="rotatable">
          <span
            class="handle"
            @mousedown.left="onAction(actions.Z)"
          >
            <RotateLeftOutlined />
            Z
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.X)"
          >
            <RotateRightOutlined />
            X
          </span>
        </template>
        <template v-else>
          <span
            class="handle"
            @mousedown.left="onAction(actions.Q)"
          >
            <ArrowLeftOutlined />
            Q
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.A)"
          >
            <ArrowDownOutlined />
            A
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.D)"
          >
            <ArrowUpOutlined />
            D
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.E)"
          >
            <ArrowRightOutlined />
            E
          </span>
        </template>
      </template>
      <template v-if="axis === '-y'">
        <template v-if="rotatable">
          <span
            class="handle"
            @mousedown.left="onAction(actions.A)"
          >
            <ArrowLeftOutlined />
            A
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.W)"
          >
            <ArrowUpOutlined />
            W
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.S)"
          >
            <ArrowDownOutlined />
            S
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.D)"
          >
            <ArrowRightOutlined />
            D
          </span>
        </template>
        <template v-else>
          <span
            class="handle"
            @mousedown.left="onAction(actions.W)"
          >
            <ArrowUpOutlined />
            W
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.S)"
          >
            <ArrowDownOutlined />
            S
          </span>
        </template>
      </template>
      <template v-if="axis === '-x'">
        <template v-if="rotatable">
          <span
            class="handle"
            @mousedown.left="onAction(actions.Q)"
          >
            <ArrowLeftOutlined />
            Q
          </span>
          <span
            class="handle"
            @mousedown.left="onAction(actions.E)"
          >
            <ArrowRightOutlined />
            E
          </span>
        </template>
      </template>
    </div>
  </div>
</template>

<style lang="scss">
.side-view {
  .handle {
    padding: 2px 4px;
    margin-right: 6px;
    pointer-events: visible;
    cursor: pointer;
    background: #1e1f23;
    border-radius: 4px;

    &:hover {
      background: #0486fe;
    }

    .anticon {
      margin-right: 4px;
    }
  }
}
</style>
