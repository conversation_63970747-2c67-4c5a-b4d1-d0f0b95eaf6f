import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

import PointsMaterial from '@/hooks/usePointCloud/PointsMaterial'

import BaseViewController from '../BaseViewController'

export const axisUpInfo = {
  x: {
    yAxis: { axis: 'z', dir: new THREE.Vector3(0, 0, 1) },
    xAxis: { axis: 'y', dir: new THREE.Vector3(0, 1, 0) },
  },
  '-x': {
    yAxis: { axis: 'z', dir: new THREE.Vector3(0, 0, 1) },
    xAxis: { axis: 'y', dir: new THREE.Vector3(0, -1, 0) },
  },
  z: {
    yAxis: { axis: 'x', dir: new THREE.Vector3(1, 0, 0) },
    xAxis: { axis: 'y', dir: new THREE.Vector3(0, -1, 0) },
  },
  // '-z': {
  //     yAxis: { axis: 'y', dir: new THREE.Vector3(0, 1, 0) },
  //     xAxis: { axis: 'x', dir: new THREE.Vector3(-1, 0, 0) },
  // },
  y: {
    yAxis: { axis: 'z', dir: new THREE.Vector3(0, 0, 1) },
    xAxis: { axis: 'x', dir: new THREE.Vector3(-1, 0, 0) },
  },
  '-y': {
    yAxis: { axis: 'z', dir: new THREE.Vector3(0, 0, 1) },
    xAxis: { axis: 'x', dir: new THREE.Vector3(1, 0, 0) },
  },
}

export type AxisType = keyof typeof axisUpInfo

export default class PointCloudSideViewController extends BaseViewController {
  static DEFAULT_OBSERVED_BOX = new THREE.Box3(
    new THREE.Vector3(-2, -2, -2),
    new THREE.Vector3(2, 2, 2),
  )

  renderer: THREE.WebGLRenderer
  camera: THREE.OrthographicCamera
  controls: OrbitControls

  points: THREE.Points

  container: HTMLDivElement | null = null
  width = 0
  height = 0

  /** 需要渲染的对象 */
  targetObject: THREE.Object3D | null = null
  /**
   * 需要聚焦观察的对象，
   * 通常是 targetObject，
   * 也可以是 targetObject 的子对象。
   */
  focusObject: THREE.Object3D | null = null
  /**
   * 被观察的空间，
   * 由 focusObject 决定，
   * 如果 focusObject 为 null 时有一个默认值。
   */
  observedBox = PointCloudSideViewController.DEFAULT_OBSERVED_BOX.clone()
  needFit = true
  enableFit = true

  axis: AxisType
  alignAxis = new THREE.Vector3()

  /**
   * 相机离被观察的对象最近点的距离，
   * 同时也是被观察的对象背后的可视距离。
   */
  offsetDistance = 0.5
  paddingPercent = 1
  selectColor = new THREE.Color(0, 1, 0)

  constructor(points: THREE.Points, axis: AxisType) {
    super()

    this.points = points
    this.axis = axis
    this.setAxis(axis)

    // renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true })
    this.renderer.autoClear = false
    this.renderer.sortObjects = false
    Object.assign(this.renderer.domElement.style, {
      position: 'absolute',
      width: '100%',
      height: '100%',
    })

    this.camera = new THREE.OrthographicCamera(-2, 2, 2, -2, 0, 10)
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableRotate = false
    this.controls.maxZoom = 10
    this.controls.minZoom = 0.2
    this.controls.addEventListener('change', () => this.render())

    this.updateViewportSize()
  }

  mount = (container: HTMLDivElement) => {
    this.container = container
    container.appendChild(this.renderer.domElement)
    this.render(true)
  }

  unmount = () => {
    if (this.container) {
      this.container.removeChild(this.renderer.domElement)
      this.container = null
    }
  }

  setTargetObject = (object: THREE.Object3D | null, focusObject = object) => {
    if (this.targetObject === object && this.focusObject === focusObject) return
    this.targetObject?.removeEventListener('change', this.fitAndRender)
    this.camera.zoom = 1
    this.enableFit = true
    this.targetObject = object
    this.focusObject = focusObject
    this.targetObject?.addEventListener('change', this.fitAndRender)
    this.fitAndRender()
  }

  fitObject = () => {
    const object = this.focusObject

    // 如果没有目标对象或禁用了自动适应，则直接返回
    if (!object) return
    if (!this.enableFit) return

    // 确保对象的世界矩阵是最新的
    object.updateMatrixWorld()

    // 1. 获取物体的包围盒和中心点
    const bbox = new THREE.Box3().setFromObject(object)
    const objectCenter = bbox.getCenter(new THREE.Vector3())

    // 2. 提取物体的旋转矩阵（不包括位置信息）
    const rotationMatrix = new THREE.Matrix4().extractRotation(object.matrixWorld)

    // 3. 计算当前视角方向的单位向量
    const viewDirection = new THREE.Vector3()

    // 根据当前轴设置视角方向
    const axisValue = this.axis.length === 2 ? this.axis[1] : this.axis[0]
    const isInverse = this.axis.length === 2
    viewDirection[axisValue as 'x' | 'y' | 'z'] = isInverse ? -1 : 1

    // 应用物体的旋转到视角方向
    viewDirection.applyMatrix4(rotationMatrix).normalize()

    // 4. 计算物体在视角方向上的最远点
    // 创建8个角点
    const corners = [
      new THREE.Vector3(bbox.min.x, bbox.min.y, bbox.min.z),
      new THREE.Vector3(bbox.min.x, bbox.min.y, bbox.max.z),
      new THREE.Vector3(bbox.min.x, bbox.max.y, bbox.min.z),
      new THREE.Vector3(bbox.min.x, bbox.max.y, bbox.max.z),
      new THREE.Vector3(bbox.max.x, bbox.min.y, bbox.min.z),
      new THREE.Vector3(bbox.max.x, bbox.min.y, bbox.max.z),
      new THREE.Vector3(bbox.max.x, bbox.max.y, bbox.min.z),
      new THREE.Vector3(bbox.max.x, bbox.max.y, bbox.max.z),
    ]

    // 找到在视角方向上最远的点
    let maxDistance = -Infinity
    const farthestPoint = new THREE.Vector3()

    for (const corner of corners) {
      // 计算从中心点到角点的向量
      const toCorner = corner.clone().sub(objectCenter)
      // 计算在视角方向上的投影距离
      const distance = toCorner.dot(viewDirection)

      if (distance > maxDistance) {
        maxDistance = distance
        // 计算最远点的位置
        farthestPoint.copy(objectCenter).add(viewDirection.clone().multiplyScalar(distance))
      }
    }

    // 5. 设置相机位置：最远点 + 固定偏移
    this.camera.position
      .copy(farthestPoint)
      .add(viewDirection.clone().multiplyScalar(this.offsetDistance))

    // 6. 设置相机的up向量
    const upVector = new THREE.Vector3()
      .copy(axisUpInfo[this.axis].yAxis.dir)
      .applyMatrix4(rotationMatrix)
    this.camera.up.copy(upVector)

    // 7. 让相机看向物体中点
    this.camera.lookAt(objectCenter)
    this.controls.target.copy(objectCenter)

    // 8. 更新投影矩形和相机参数
    this.updateObservedBox()
    this.updateCamera()
  }

  fitAndRender = () => {
    this.needFit = true
    this.render()
  }

  updateObservedBox = () => {
    if (!this.focusObject) {
      this.observedBox.copy(PointCloudSideViewController.DEFAULT_OBSERVED_BOX)
      return
    }

    const { focusObject: object, camera } = this

    camera.updateMatrixWorld()
    object.updateMatrixWorld()
    // 用 setFromObject 计算世界坐标下的包围盒
    const bbox = new THREE.Box3().setFromObject(object)

    // 拷贝顶点进行投影
    const minProject = bbox.min.clone().applyMatrix4(camera.matrixWorldInverse)
    const maxProject = bbox.max.clone().applyMatrix4(camera.matrixWorldInverse)

    // 构造项目空间下的 Axis-Aligned Bounding Box
    const min = new THREE.Vector3(
      Math.min(minProject.x, maxProject.x),
      Math.min(minProject.y, maxProject.y),
      Math.min(minProject.z, maxProject.z),
    ).addScalar(-this.offsetDistance)
    const max = new THREE.Vector3(
      Math.max(minProject.x, maxProject.x),
      Math.max(minProject.y, maxProject.y),
      Math.max(minProject.z, maxProject.z),
    ).addScalar(this.offsetDistance)
    this.observedBox.min.copy(min)
    this.observedBox.max.copy(max)
  }

  updateCamera = () => {
    const observedBox = this.observedBox
    const rectWidth = observedBox.max.x - observedBox.min.x
    const rectHeight = observedBox.max.y - observedBox.min.y
    const aspect = this.width / this.height

    const padding = Math.min(rectWidth, rectHeight) * this.paddingPercent
    const cameraW = Math.max(rectWidth + padding, (rectHeight + padding) * aspect)
    const cameraH = Math.max(rectHeight + padding, (rectWidth + padding) / aspect)

    this.camera.left = -cameraW / 2
    this.camera.right = cameraW / 2
    this.camera.top = cameraH / 2
    this.camera.bottom = -cameraH / 2
    this.camera.far = observedBox.max.z - observedBox.min.z + this.offsetDistance * 2
    this.camera.updateProjectionMatrix()

    this.controls.update()
  }

  updateViewportSize = () => {
    const { clientWidth: width, clientHeight: height } = this.renderer.domElement

    if (width === this.width && height === this.height) {
      return
    }
    this.width = width
    this.height = height
    this.renderer.setSize(this.width, this.height, false)
    this.updateCamera()
  }

  setAxis = (axis: AxisType) => {
    this.axis = axis
    this.alignAxis.set(0, 0, 0)

    const axisValue = this.axis.length === 2 ? this.axis[1] : this.axis[0]
    const isInverse = this.axis.length === 2
    this.alignAxis[axisValue as 'x' | 'y' | 'z'] = isInverse ? -0.5 : 0.5

    this.fitAndRender()
  }

  cameraToCanvas = (pos: THREE.Vector3) => {
    pos.applyMatrix4(this.camera.projectionMatrix)
    pos.x = ((pos.x + 1) / 2) * this.width
    pos.y = (-(pos.y - 1) / 2) * this.height
    return pos
  }

  canvasToCamera = (pos: THREE.Vector3) => {
    pos.x = (pos.x / this.width) * 2 - 1
    pos.y = ((-1 * pos.y) / this.height) * 2 + 1

    pos.x *= this.camera.right - this.camera.left
    pos.y *= this.camera.top - this.camera.bottom
    return pos
  }

  highlightPoints = (points: THREE.Points, target: THREE.Object3D) => {
    if (!(target instanceof THREE.Mesh)) {
      return () => {}
    }
    const bbox = new THREE.Box3().setFromObject(target)
    const material = points.material as PointsMaterial

    const oldDepthTest = material.depthTest
    const oldHasFilterBox = material.uniforms.hasFilterBox.value
    const oldType = material.uniforms.boxInfo.value.type

    material.depthTest = false
    material.setUniforms({
      hasFilterBox: 1,
      boxInfo: {
        type: 0,
        min: bbox.min,
        max: bbox.max,
        color: this.selectColor,
        matrix: target.matrixWorld.clone().invert(),
      },
    })
    return () => {
      material.setUniforms({ hasFilterBox: oldHasFilterBox, boxInfo: { type: oldType } })
      material.depthTest = oldDepthTest
    }
  }

  // render
  renderFrame = () => {
    if (!this.container) return

    this.updateViewportSize()

    if (this.needFit) {
      this.fitObject()
      this.needFit = false
    }
    const { points, targetObject, camera, renderer } = this

    renderer.clear(true, true, true)

    if (targetObject) {
      targetObject.updateMatrixWorld()
      const restore = this.highlightPoints(points, targetObject)
      renderer.render(points, camera)
      restore()

      renderer.render(targetObject, camera)
    } else {
      renderer.render(points, camera)
    }
  }
}
