<script setup lang="ts">
import PointCloudSideView from './PointCloudSideView.vue'
import type { AxisType } from './PointCloudSideViewController'

defineProps<{
  pointCloud: THREE.Points
  objects?: THREE.Object3D[]
  focusedTarget: THREE.Object3D | null | undefined
}>()

const axisList: AxisType[] = ['z', '-y', '-x']
</script>

<template>
  <div class="side-view-wrap flex h-full flex-col divide-y divide-solid bg-black px-1.5">
    <PointCloudSideView
      v-for="axis in axisList"
      :key="axis"
      :axis="axis"
      class="border-[#2a2a2a]"
      :point-cloud="pointCloud"
      :target="focusedTarget"
    />
  </div>
</template>
