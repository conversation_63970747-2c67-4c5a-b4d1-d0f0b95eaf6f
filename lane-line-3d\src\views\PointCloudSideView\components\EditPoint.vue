<script setup lang="ts">
import { computed } from 'vue'

import { GeometryTypes } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'
import type { IPoint3D } from '@/objects/Point3D'
import useSelection from '@/stores/useSelection'

import type PointCloudSideViewController from '../PointCloudSideViewController'
import PointSimulation from './PointSimulation.vue'

defineProps<{
  controller: PointCloudSideViewController
}>()

const editor = useEditor()

const { selectedGeometries } = useSelection()

const editingObject = computed(() => {
  if (selectedGeometries.value?.length !== 1) return
  if (selectedGeometries.value[0].type !== GeometryTypes.Point3D) return
  return selectedGeometries.value[0]
})

const handleUpdate = (pos: [number, number, number]) => {
  // 更新几何体
  editor.utils.updateGeometryById(editingObject.value!.id, (geometry) => {
    ;(geometry as IPoint3D).shape.point = pos
  })
}
</script>

<template>
  <PointSimulation
    v-if="editingObject"
    :controller="controller"
    :point="editingObject.shape.point"
    @update:point="handleUpdate"
  />
</template>

<style scoped lang="scss"></style>
