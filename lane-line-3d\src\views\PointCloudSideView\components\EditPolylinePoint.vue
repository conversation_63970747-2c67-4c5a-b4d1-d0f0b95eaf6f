<script setup lang="ts">
import { computed } from 'vue'

import { GeometryTypes } from '@/const'
import useEditor from '@/editors/LaneLine3D/useEditor'
import type { IPolyline3D } from '@/objects/Polyline3D'
import useSelection from '@/stores/useSelection'
import usePolylinePointSelection from '@/views/PointCloudMainView/hooks/usePolylinePointSelection'

import type PointCloudSideViewController from '../PointCloudSideViewController'
import PointSimulation from './PointSimulation.vue'

defineProps<{
  controller: PointCloudSideViewController
}>()

const editor = useEditor()

const selectedPointIndex = usePolylinePointSelection().selectedPointIndex

const { selectedGeometries } = useSelection()

const editingObject = computed(() => {
  if (selectedGeometries.value?.length !== 1) return
  if (
    selectedGeometries.value[0].type !== GeometryTypes.Polyline3D &&
    selectedGeometries.value[0].type !== GeometryTypes.Polygon3D
  )
    return
  return selectedGeometries.value[0]
})

const handleUpdate = (pos: [number, number, number]) => {
  // 更新几何体
  editor.utils.updateGeometryById(editingObject.value!.id, (geometry) => {
    ;(geometry as IPolyline3D).shape.points[selectedPointIndex.value] = pos
  })
}
</script>

<template>
  <PointSimulation
    v-if="editingObject && editingObject.shape.points[selectedPointIndex]"
    :controller="controller"
    :point="editingObject.shape.points[selectedPointIndex]"
    @update:point="handleUpdate"
  />
</template>

<style scoped lang="scss"></style>
