<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import * as THREE from 'three'
import { computed, ref } from 'vue'

import InteractiveCircle from '@/components/InteractiveCircle.vue'
import { ViewEvent } from '@/const'
import { worldToCanvas } from '@/utils/three-utils'

import type PointCloudSideViewController from '../PointCloudSideViewController'

const props = defineProps<{
  controller: PointCloudSideViewController
  point: [number, number, number]
}>()
const emit = defineEmits(['update:point'])

const renderIndex = ref(0)

useEventListener(
  () => props.controller,
  ViewEvent.RENDER_AFTER,
  () => {
    renderIndex.value++
  },
)

const point = computed(() => {
  if (!props.point) return
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  renderIndex.value // force update
  const p = props.point
  const canvasPos = worldToCanvas(
    props.controller.renderer.domElement,
    props.controller.camera,
    new THREE.Vector3(p[0], p[1], p[2]),
  )
  return [canvasPos.x, canvasPos.y] as [number, number]
})

const handleMove = (screenPos: { x: number; y: number }) => {
  // 获取当前编辑对象的位置作为基准
  const p = props.point
  const worldPos = movePointToScreenPosition(
    props.controller.camera,
    props.controller.renderer.domElement,
    screenPos.x,
    screenPos.y,
    new THREE.Vector3(p[0], p[1], p[2]),
  )

  emit('update:point', [worldPos.x, worldPos.y, worldPos.z])
}

function movePointToScreenPosition(
  camera: THREE.OrthographicCamera,
  rendererDom: HTMLCanvasElement,
  screenX: number,
  screenY: number,
  oldWorldPos: THREE.Vector3,
): THREE.Vector3 {
  const rect = rendererDom.getBoundingClientRect()

  // 1. 将原始世界坐标投影到 NDC（归一化设备坐标）
  const ndc = oldWorldPos.clone().project(camera)

  // 2. 将目标 screen 坐标转成 NDC（保留 Z 不变）
  const xNdc = ((screenX - rect.left) / rect.width) * 2 - 1
  const yNdc = -((screenY - rect.top) / rect.height) * 2 + 1

  const targetNdc = new THREE.Vector3(xNdc, yNdc, ndc.z)

  // 3. 将新的 NDC 转回世界坐标
  return targetNdc.unproject(camera)
}

const handleMoveStart = () => {
  const { controller } = props
  controller.enableFit = false
}

const handleMoveEnd = () => {
  const { controller } = props
  controller.enableFit = true
  controller.fitAndRender()
}
</script>

<template>
  <svg
    v-if="point"
    class="pointer-events-none absolute h-full w-full"
    xmlns="http://www.w3.org/2000/svg"
  >
    <InteractiveCircle
      active
      draggable
      :point="point"
      @move="handleMove"
      @move-end="handleMoveEnd"
      @move-start="handleMoveStart"
    />
  </svg>
</template>

<style scoped lang="scss"></style>
